{"version": 3, "middleware": {}, "sortedMiddleware": [], "functions": {"/health/route": {"files": ["server/edge/chunks/9583c_@sentry_core_build_esm_1adfbc63._.js", "server/edge/chunks/4b803_@sentry_vercel-edge_build_esm_index_de3a4916.js", "server/edge/chunks/ec4b9_zod_dist_esm_d6b871ea._.js", "server/edge/chunks/node_modules__pnpm_096b6372._.js", "server/edge/chunks/[root-of-the-server]__b6d13720._.js", "server/edge/chunks/apps_api_edge-wrapper_f661de81.js", "server/server-reference-manifest.js", "server/middleware-build-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/app/health/route_client-reference-manifest.js", "server/edge/chunks/apps_api__next-internal_server_app_health_route_actions_86bed8d0.js", "server/edge/chunks/apps_api__next-internal_server_app_health_route_actions_e21d0879.js", "server/edge/chunks/661a5_next_dist_esm_fb911311._.js", "server/edge/chunks/661a5_next_dist_compiled_7469dbd8._.js", "server/edge/chunks/661a5_next_dist_63b4f122._.js", "server/edge/chunks/apps_api_edge-wrapper_f11daf2c.js", "server/edge/chunks/[root-of-the-server]__ad8dc959._.js", "server/edge/chunks/apps_api_edge-wrapper_a425c1c6.js", "server/app/health/route/react-loadable-manifest.js"], "name": "/health", "page": "/health/route", "matchers": [{"regexp": "^/health(?:/)?$", "originalSource": "/health"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "6uP+wnKOqJvASFUst48I01f3G7SuLLAd8EXVuTEVo28=", "__NEXT_PREVIEW_MODE_ID": "c2410ebecd5c055eceac7b5f3997e889", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7541e39e2d8e2d704c43ded7a0630a0157dc3d97fea1a8902323afd08b913414", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "2981e6b4ca9602caceae675f81acf7f3ad08bace82a71b69a1e37debc0cb6088"}}}}