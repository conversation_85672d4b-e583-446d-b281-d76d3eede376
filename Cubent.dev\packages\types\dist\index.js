// src/providers/anthropic.ts
var anthropicDefaultModelId = "claude-sonnet-4-20250514";
var anthropicModels = {
  "claude-sonnet-4-20250514": {
    maxTokens: 64e3,
    // Overridden to 8k if `enableReasoningEffort` is false.
    contextWindow: 2e5,
    supportsImages: true,
    supportsComputerUse: true,
    supportsPromptCache: true,
    inputPrice: 3,
    // $3 per million input tokens
    outputPrice: 15,
    // $15 per million output tokens
    cacheWritesPrice: 3.75,
    // $3.75 per million tokens
    cacheReadsPrice: 0.3,
    // $0.30 per million tokens
    supportsReasoningBudget: true
  },
  "claude-opus-4-20250514": {
    maxTokens: 32e3,
    // Overridden to 8k if `enableReasoningEffort` is false.
    contextWindow: 2e5,
    supportsImages: true,
    supportsComputerUse: true,
    supportsPromptCache: true,
    inputPrice: 15,
    // $15 per million input tokens
    outputPrice: 75,
    // $75 per million output tokens
    cacheWritesPrice: 18.75,
    // $18.75 per million tokens
    cacheReadsPrice: 1.5,
    // $1.50 per million tokens
    supportsReasoningBudget: true
  },
  "claude-3-7-sonnet-20250219:thinking": {
    maxTokens: 128e3,
    // Unlocked by passing `beta` flag to the model. Otherwise, it's 64k.
    contextWindow: 2e5,
    supportsImages: true,
    supportsComputerUse: true,
    supportsPromptCache: true,
    inputPrice: 3,
    // $3 per million input tokens
    outputPrice: 15,
    // $15 per million output tokens
    cacheWritesPrice: 3.75,
    // $3.75 per million tokens
    cacheReadsPrice: 0.3,
    // $0.30 per million tokens
    supportsReasoningBudget: true,
    requiredReasoningBudget: true
  },
  "claude-3-7-sonnet-20250219": {
    maxTokens: 8192,
    // Since we already have a `:thinking` virtual model we aren't setting `supportsReasoningBudget: true` here.
    contextWindow: 2e5,
    supportsImages: true,
    supportsComputerUse: true,
    supportsPromptCache: true,
    inputPrice: 3,
    // $3 per million input tokens
    outputPrice: 15,
    // $15 per million output tokens
    cacheWritesPrice: 3.75,
    // $3.75 per million tokens
    cacheReadsPrice: 0.3
    // $0.30 per million tokens
  },
  "claude-3-5-sonnet-20241022": {
    maxTokens: 8192,
    contextWindow: 2e5,
    supportsImages: true,
    supportsComputerUse: true,
    supportsPromptCache: true,
    inputPrice: 3,
    // $3 per million input tokens
    outputPrice: 15,
    // $15 per million output tokens
    cacheWritesPrice: 3.75,
    // $3.75 per million tokens
    cacheReadsPrice: 0.3
    // $0.30 per million tokens
  },
  "claude-3-5-haiku-20241022": {
    maxTokens: 8192,
    contextWindow: 2e5,
    supportsImages: false,
    supportsPromptCache: true,
    inputPrice: 1,
    outputPrice: 5,
    cacheWritesPrice: 1.25,
    cacheReadsPrice: 0.1
  },
  "claude-3-opus-20240229": {
    maxTokens: 4096,
    contextWindow: 2e5,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 15,
    outputPrice: 75,
    cacheWritesPrice: 18.75,
    cacheReadsPrice: 1.5
  },
  "claude-3-haiku-20240307": {
    maxTokens: 4096,
    contextWindow: 2e5,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 0.25,
    outputPrice: 1.25,
    cacheWritesPrice: 0.3,
    cacheReadsPrice: 0.03
  }
};
var ANTHROPIC_DEFAULT_MAX_TOKENS = 8192;

// src/providers/bedrock.ts
var bedrockDefaultModelId = "anthropic.claude-sonnet-4-20250514-v1:0";
var bedrockDefaultPromptRouterModelId = "anthropic.claude-3-sonnet-20240229-v1:0";
var bedrockModels = {
  "amazon.nova-pro-v1:0": {
    maxTokens: 5e3,
    contextWindow: 3e5,
    supportsImages: true,
    supportsComputerUse: false,
    supportsPromptCache: true,
    inputPrice: 0.8,
    outputPrice: 3.2,
    cacheWritesPrice: 0.8,
    // per million tokens
    cacheReadsPrice: 0.2,
    // per million tokens
    minTokensPerCachePoint: 1,
    maxCachePoints: 1,
    cachableFields: ["system"]
  },
  "amazon.nova-pro-latency-optimized-v1:0": {
    maxTokens: 5e3,
    contextWindow: 3e5,
    supportsImages: true,
    supportsComputerUse: false,
    supportsPromptCache: false,
    inputPrice: 1,
    outputPrice: 4,
    cacheWritesPrice: 1,
    // per million tokens
    cacheReadsPrice: 0.25,
    // per million tokens
    description: "Amazon Nova Pro with latency optimized inference"
  },
  "amazon.nova-lite-v1:0": {
    maxTokens: 5e3,
    contextWindow: 3e5,
    supportsImages: true,
    supportsComputerUse: false,
    supportsPromptCache: true,
    inputPrice: 0.06,
    outputPrice: 0.24,
    cacheWritesPrice: 0.06,
    // per million tokens
    cacheReadsPrice: 0.015,
    // per million tokens
    minTokensPerCachePoint: 1,
    maxCachePoints: 1,
    cachableFields: ["system"]
  },
  "amazon.nova-micro-v1:0": {
    maxTokens: 5e3,
    contextWindow: 128e3,
    supportsImages: false,
    supportsComputerUse: false,
    supportsPromptCache: true,
    inputPrice: 0.035,
    outputPrice: 0.14,
    cacheWritesPrice: 0.035,
    // per million tokens
    cacheReadsPrice: 875e-5,
    // per million tokens
    minTokensPerCachePoint: 1,
    maxCachePoints: 1,
    cachableFields: ["system"]
  },
  "anthropic.claude-sonnet-4-20250514-v1:0": {
    maxTokens: 8192,
    contextWindow: 2e5,
    supportsImages: true,
    supportsComputerUse: true,
    supportsPromptCache: true,
    inputPrice: 3,
    outputPrice: 15,
    cacheWritesPrice: 3.75,
    cacheReadsPrice: 0.3,
    minTokensPerCachePoint: 1024,
    maxCachePoints: 4,
    cachableFields: ["system", "messages", "tools"]
  },
  "anthropic.claude-opus-4-20250514-v1:0": {
    maxTokens: 8192,
    contextWindow: 2e5,
    supportsImages: true,
    supportsComputerUse: true,
    supportsPromptCache: true,
    inputPrice: 15,
    outputPrice: 75,
    cacheWritesPrice: 18.75,
    cacheReadsPrice: 1.5,
    minTokensPerCachePoint: 1024,
    maxCachePoints: 4,
    cachableFields: ["system", "messages", "tools"]
  },
  "anthropic.claude-3-7-sonnet-20250219-v1:0": {
    maxTokens: 8192,
    contextWindow: 2e5,
    supportsImages: true,
    supportsComputerUse: true,
    supportsPromptCache: true,
    inputPrice: 3,
    outputPrice: 15,
    cacheWritesPrice: 3.75,
    cacheReadsPrice: 0.3,
    minTokensPerCachePoint: 1024,
    maxCachePoints: 4,
    cachableFields: ["system", "messages", "tools"]
  },
  "anthropic.claude-3-5-sonnet-20241022-v2:0": {
    maxTokens: 8192,
    contextWindow: 2e5,
    supportsImages: true,
    supportsComputerUse: true,
    supportsPromptCache: true,
    inputPrice: 3,
    outputPrice: 15,
    cacheWritesPrice: 3.75,
    cacheReadsPrice: 0.3,
    minTokensPerCachePoint: 1024,
    maxCachePoints: 4,
    cachableFields: ["system", "messages", "tools"]
  },
  "anthropic.claude-3-5-haiku-20241022-v1:0": {
    maxTokens: 8192,
    contextWindow: 2e5,
    supportsImages: false,
    supportsPromptCache: true,
    inputPrice: 0.8,
    outputPrice: 4,
    cacheWritesPrice: 1,
    cacheReadsPrice: 0.08,
    minTokensPerCachePoint: 2048,
    maxCachePoints: 4,
    cachableFields: ["system", "messages", "tools"]
  },
  "anthropic.claude-3-5-sonnet-20240620-v1:0": {
    maxTokens: 8192,
    contextWindow: 2e5,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 3,
    outputPrice: 15
  },
  "anthropic.claude-3-opus-20240229-v1:0": {
    maxTokens: 4096,
    contextWindow: 2e5,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 15,
    outputPrice: 75
  },
  "anthropic.claude-3-sonnet-20240229-v1:0": {
    maxTokens: 4096,
    contextWindow: 2e5,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 3,
    outputPrice: 15
  },
  "anthropic.claude-3-haiku-20240307-v1:0": {
    maxTokens: 4096,
    contextWindow: 2e5,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0.25,
    outputPrice: 1.25
  },
  "anthropic.claude-2-1-v1:0": {
    maxTokens: 4096,
    contextWindow: 1e5,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 8,
    outputPrice: 24,
    description: "Claude 2.1"
  },
  "anthropic.claude-2-0-v1:0": {
    maxTokens: 4096,
    contextWindow: 1e5,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 8,
    outputPrice: 24,
    description: "Claude 2.0"
  },
  "anthropic.claude-instant-v1:0": {
    maxTokens: 4096,
    contextWindow: 1e5,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0.8,
    outputPrice: 2.4,
    description: "Claude Instant"
  },
  "deepseek.r1-v1:0": {
    maxTokens: 32768,
    contextWindow: 128e3,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 1.35,
    outputPrice: 5.4
  },
  "meta.llama3-3-70b-instruct-v1:0": {
    maxTokens: 8192,
    contextWindow: 128e3,
    supportsImages: false,
    supportsComputerUse: false,
    supportsPromptCache: false,
    inputPrice: 0.72,
    outputPrice: 0.72,
    description: "Llama 3.3 Instruct (70B)"
  },
  "meta.llama3-2-90b-instruct-v1:0": {
    maxTokens: 8192,
    contextWindow: 128e3,
    supportsImages: true,
    supportsComputerUse: false,
    supportsPromptCache: false,
    inputPrice: 0.72,
    outputPrice: 0.72,
    description: "Llama 3.2 Instruct (90B)"
  },
  "meta.llama3-2-11b-instruct-v1:0": {
    maxTokens: 8192,
    contextWindow: 128e3,
    supportsImages: true,
    supportsComputerUse: false,
    supportsPromptCache: false,
    inputPrice: 0.16,
    outputPrice: 0.16,
    description: "Llama 3.2 Instruct (11B)"
  },
  "meta.llama3-2-3b-instruct-v1:0": {
    maxTokens: 8192,
    contextWindow: 128e3,
    supportsImages: false,
    supportsComputerUse: false,
    supportsPromptCache: false,
    inputPrice: 0.15,
    outputPrice: 0.15,
    description: "Llama 3.2 Instruct (3B)"
  },
  "meta.llama3-2-1b-instruct-v1:0": {
    maxTokens: 8192,
    contextWindow: 128e3,
    supportsImages: false,
    supportsComputerUse: false,
    supportsPromptCache: false,
    inputPrice: 0.1,
    outputPrice: 0.1,
    description: "Llama 3.2 Instruct (1B)"
  },
  "meta.llama3-1-405b-instruct-v1:0": {
    maxTokens: 8192,
    contextWindow: 128e3,
    supportsImages: false,
    supportsComputerUse: false,
    supportsPromptCache: false,
    inputPrice: 2.4,
    outputPrice: 2.4,
    description: "Llama 3.1 Instruct (405B)"
  },
  "meta.llama3-1-70b-instruct-v1:0": {
    maxTokens: 8192,
    contextWindow: 128e3,
    supportsImages: false,
    supportsComputerUse: false,
    supportsPromptCache: false,
    inputPrice: 0.72,
    outputPrice: 0.72,
    description: "Llama 3.1 Instruct (70B)"
  },
  "meta.llama3-1-70b-instruct-latency-optimized-v1:0": {
    maxTokens: 8192,
    contextWindow: 128e3,
    supportsImages: false,
    supportsComputerUse: false,
    supportsPromptCache: false,
    inputPrice: 0.9,
    outputPrice: 0.9,
    description: "Llama 3.1 Instruct (70B) (w/ latency optimized inference)"
  },
  "meta.llama3-1-8b-instruct-v1:0": {
    maxTokens: 8192,
    contextWindow: 8e3,
    supportsImages: false,
    supportsComputerUse: false,
    supportsPromptCache: false,
    inputPrice: 0.22,
    outputPrice: 0.22,
    description: "Llama 3.1 Instruct (8B)"
  },
  "meta.llama3-70b-instruct-v1:0": {
    maxTokens: 2048,
    contextWindow: 8e3,
    supportsImages: false,
    supportsComputerUse: false,
    supportsPromptCache: false,
    inputPrice: 2.65,
    outputPrice: 3.5
  },
  "meta.llama3-8b-instruct-v1:0": {
    maxTokens: 2048,
    contextWindow: 4e3,
    supportsImages: false,
    supportsComputerUse: false,
    supportsPromptCache: false,
    inputPrice: 0.3,
    outputPrice: 0.6
  },
  "amazon.titan-text-lite-v1:0": {
    maxTokens: 4096,
    contextWindow: 8e3,
    supportsImages: false,
    supportsComputerUse: false,
    supportsPromptCache: false,
    inputPrice: 0.15,
    outputPrice: 0.2,
    description: "Amazon Titan Text Lite"
  },
  "amazon.titan-text-express-v1:0": {
    maxTokens: 4096,
    contextWindow: 8e3,
    supportsImages: false,
    supportsComputerUse: false,
    supportsPromptCache: false,
    inputPrice: 0.2,
    outputPrice: 0.6,
    description: "Amazon Titan Text Express"
  },
  "amazon.titan-text-embeddings-v1:0": {
    maxTokens: 8192,
    contextWindow: 8e3,
    supportsImages: false,
    supportsComputerUse: false,
    supportsPromptCache: false,
    inputPrice: 0.1,
    description: "Amazon Titan Text Embeddings"
  },
  "amazon.titan-text-embeddings-v2:0": {
    maxTokens: 8192,
    contextWindow: 8e3,
    supportsImages: false,
    supportsComputerUse: false,
    supportsPromptCache: false,
    inputPrice: 0.02,
    description: "Amazon Titan Text Embeddings V2"
  }
};
var BEDROCK_DEFAULT_TEMPERATURE = 0.3;
var BEDROCK_MAX_TOKENS = 4096;
var BEDROCK_REGION_INFO = {
  /*
   * This JSON generated by AWS's AI assistant - Amazon Q on March 29, 2025
   *
   *  - Africa (Cape Town) region does not appear to support Amazon Bedrock at this time.
   *  - Some Asia Pacific regions, such as Asia Pacific (Hong Kong) and Asia Pacific (Jakarta), are not listed among the supported regions for Bedrock services.
   *  - Middle East regions, including Middle East (Bahrain) and Middle East (UAE), are not mentioned in the list of supported regions for Bedrock. [3]
   *  - China regions (Beijing and Ningxia) are not listed as supported for Amazon Bedrock.
   *  - Some newer or specialized AWS regions may not have Bedrock support yet.
   */
  "us.": { regionId: "us-east-1", description: "US East (N. Virginia)", pattern: "us-", multiRegion: true },
  "use.": { regionId: "us-east-1", description: "US East (N. Virginia)" },
  "use1.": { regionId: "us-east-1", description: "US East (N. Virginia)" },
  "use2.": { regionId: "us-east-2", description: "US East (Ohio)" },
  "usw.": { regionId: "us-west-2", description: "US West (Oregon)" },
  "usw2.": { regionId: "us-west-2", description: "US West (Oregon)" },
  "ug.": {
    regionId: "us-gov-west-1",
    description: "AWS GovCloud (US-West)",
    pattern: "us-gov-",
    multiRegion: true
  },
  "uge1.": { regionId: "us-gov-east-1", description: "AWS GovCloud (US-East)" },
  "ugw1.": { regionId: "us-gov-west-1", description: "AWS GovCloud (US-West)" },
  "eu.": { regionId: "eu-west-1", description: "Europe (Ireland)", pattern: "eu-", multiRegion: true },
  "euw1.": { regionId: "eu-west-1", description: "Europe (Ireland)" },
  "euw2.": { regionId: "eu-west-2", description: "Europe (London)" },
  "euw3.": { regionId: "eu-west-3", description: "Europe (Paris)" },
  "euc1.": { regionId: "eu-central-1", description: "Europe (Frankfurt)" },
  "euc2.": { regionId: "eu-central-2", description: "Europe (Zurich)" },
  "eun1.": { regionId: "eu-north-1", description: "Europe (Stockholm)" },
  "eus1.": { regionId: "eu-south-1", description: "Europe (Milan)" },
  "eus2.": { regionId: "eu-south-2", description: "Europe (Spain)" },
  "ap.": {
    regionId: "ap-southeast-1",
    description: "Asia Pacific (Singapore)",
    pattern: "ap-",
    multiRegion: true
  },
  "ape1.": { regionId: "ap-east-1", description: "Asia Pacific (Hong Kong)" },
  "apne1.": { regionId: "ap-northeast-1", description: "Asia Pacific (Tokyo)" },
  "apne2.": { regionId: "ap-northeast-2", description: "Asia Pacific (Seoul)" },
  "apne3.": { regionId: "ap-northeast-3", description: "Asia Pacific (Osaka)" },
  "aps1.": { regionId: "ap-south-1", description: "Asia Pacific (Mumbai)" },
  "aps2.": { regionId: "ap-south-2", description: "Asia Pacific (Hyderabad)" },
  "apse1.": { regionId: "ap-southeast-1", description: "Asia Pacific (Singapore)" },
  "apse2.": { regionId: "ap-southeast-2", description: "Asia Pacific (Sydney)" },
  "ca.": { regionId: "ca-central-1", description: "Canada (Central)", pattern: "ca-", multiRegion: true },
  "cac1.": { regionId: "ca-central-1", description: "Canada (Central)" },
  "sa.": { regionId: "sa-east-1", description: "South America (S\xE3o Paulo)", pattern: "sa-", multiRegion: true },
  "sae1.": { regionId: "sa-east-1", description: "South America (S\xE3o Paulo)" },
  // These are not official - they weren't generated by Amazon Q nor were
  // found in the AWS documentation but another cubent contributor found apac.
  // Was needed so I've added the pattern of the other geo zones.
  "apac.": { regionId: "ap-southeast-1", description: "Default APAC region", pattern: "ap-", multiRegion: true },
  "emea.": { regionId: "eu-west-1", description: "Default EMEA region", pattern: "eu-", multiRegion: true },
  "amer.": { regionId: "us-east-1", description: "Default Americas region", pattern: "us-", multiRegion: true }
};
var BEDROCK_REGIONS = Object.values(BEDROCK_REGION_INFO).map((info) => ({ value: info.regionId, label: info.regionId })).filter((region, index, self) => index === self.findIndex((r) => r.value === region.value)).sort((a, b) => a.value.localeCompare(b.value));

// src/providers/chutes.ts
var chutesDefaultModelId = "deepseek-ai/DeepSeek-R1-0528";
var chutesModels = {
  "deepseek-ai/DeepSeek-R1-0528": {
    maxTokens: 32768,
    contextWindow: 163840,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "DeepSeek R1 0528 model."
  },
  "deepseek-ai/DeepSeek-R1": {
    maxTokens: 32768,
    contextWindow: 163840,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "DeepSeek R1 model."
  },
  "deepseek-ai/DeepSeek-V3": {
    maxTokens: 32768,
    contextWindow: 163840,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "DeepSeek V3 model."
  },
  "unsloth/Llama-3.3-70B-Instruct": {
    maxTokens: 32768,
    // From Groq
    contextWindow: 131072,
    // From Groq
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "Unsloth Llama 3.3 70B Instruct model."
  },
  "chutesai/Llama-4-Scout-17B-16E-Instruct": {
    maxTokens: 32768,
    contextWindow: 512e3,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "ChutesAI Llama 4 Scout 17B Instruct model, 512K context."
  },
  "unsloth/Mistral-Nemo-Instruct-2407": {
    maxTokens: 32768,
    contextWindow: 128e3,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "Unsloth Mistral Nemo Instruct model."
  },
  "unsloth/gemma-3-12b-it": {
    maxTokens: 32768,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "Unsloth Gemma 3 12B IT model."
  },
  "NousResearch/DeepHermes-3-Llama-3-8B-Preview": {
    maxTokens: 32768,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "Nous DeepHermes 3 Llama 3 8B Preview model."
  },
  "unsloth/gemma-3-4b-it": {
    maxTokens: 32768,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "Unsloth Gemma 3 4B IT model."
  },
  "nvidia/Llama-3_3-Nemotron-Super-49B-v1": {
    maxTokens: 32768,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "Nvidia Llama 3.3 Nemotron Super 49B model."
  },
  "nvidia/Llama-3_1-Nemotron-Ultra-253B-v1": {
    maxTokens: 32768,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "Nvidia Llama 3.1 Nemotron Ultra 253B model."
  },
  "chutesai/Llama-4-Maverick-17B-128E-Instruct-FP8": {
    maxTokens: 32768,
    contextWindow: 256e3,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "ChutesAI Llama 4 Maverick 17B Instruct FP8 model."
  },
  "deepseek-ai/DeepSeek-V3-Base": {
    maxTokens: 32768,
    contextWindow: 163840,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "DeepSeek V3 Base model."
  },
  "deepseek-ai/DeepSeek-R1-Zero": {
    maxTokens: 32768,
    contextWindow: 163840,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "DeepSeek R1 Zero model."
  },
  "deepseek-ai/DeepSeek-V3-0324": {
    maxTokens: 32768,
    contextWindow: 163840,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "DeepSeek V3 (0324) model."
  },
  "Qwen/Qwen3-235B-A22B": {
    maxTokens: 32768,
    contextWindow: 40960,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "Qwen3 235B A22B model."
  },
  "Qwen/Qwen3-32B": {
    maxTokens: 32768,
    contextWindow: 40960,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "Qwen3 32B model."
  },
  "Qwen/Qwen3-30B-A3B": {
    maxTokens: 32768,
    contextWindow: 40960,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "Qwen3 30B A3B model."
  },
  "Qwen/Qwen3-14B": {
    maxTokens: 32768,
    contextWindow: 40960,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "Qwen3 14B model."
  },
  "Qwen/Qwen3-8B": {
    maxTokens: 32768,
    contextWindow: 40960,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "Qwen3 8B model."
  },
  "microsoft/MAI-DS-R1-FP8": {
    maxTokens: 32768,
    contextWindow: 163840,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "Microsoft MAI-DS-R1 FP8 model."
  },
  "tngtech/DeepSeek-R1T-Chimera": {
    maxTokens: 32768,
    contextWindow: 163840,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "TNGTech DeepSeek R1T Chimera model."
  }
};

// src/providers/deepseek.ts
var deepSeekDefaultModelId = "deepseek-chat";
var deepSeekModels = {
  "deepseek-chat": {
    maxTokens: 8192,
    contextWindow: 64e3,
    supportsImages: false,
    supportsPromptCache: true,
    inputPrice: 0.27,
    // $0.27 per million tokens (cache miss)
    outputPrice: 1.1,
    // $1.10 per million tokens
    cacheWritesPrice: 0.27,
    // $0.27 per million tokens (cache miss)
    cacheReadsPrice: 0.07,
    // $0.07 per million tokens (cache hit).
    description: `DeepSeek-V3 achieves a significant breakthrough in inference speed over previous models. It tops the leaderboard among open-source models and rivals the most advanced closed-source models globally.`
  },
  "deepseek-reasoner": {
    maxTokens: 8192,
    contextWindow: 64e3,
    supportsImages: false,
    supportsPromptCache: true,
    inputPrice: 0.55,
    // $0.55 per million tokens (cache miss)
    outputPrice: 2.19,
    // $2.19 per million tokens
    cacheWritesPrice: 0.55,
    // $0.55 per million tokens (cache miss)
    cacheReadsPrice: 0.14,
    // $0.14 per million tokens (cache hit)
    description: `DeepSeek-R1 achieves performance comparable to OpenAI-o1 across math, code, and reasoning tasks. Supports Chain of Thought reasoning with up to 32K tokens.`
  }
};
var DEEP_SEEK_DEFAULT_TEMPERATURE = 0.6;

// src/providers/gemini.ts
var geminiDefaultModelId = "gemini-2.0-flash-001";
var geminiModels = {
  "gemini-2.5-flash-preview-04-17:thinking": {
    maxTokens: 65535,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0.15,
    outputPrice: 3.5,
    maxThinkingTokens: 24576,
    supportsReasoningBudget: true,
    requiredReasoningBudget: true
  },
  "gemini-2.5-flash-preview-04-17": {
    maxTokens: 65535,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0.15,
    outputPrice: 0.6
  },
  "gemini-2.5-flash-preview-05-20:thinking": {
    maxTokens: 65535,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 0.15,
    outputPrice: 3.5,
    cacheReadsPrice: 0.0375,
    cacheWritesPrice: 1,
    maxThinkingTokens: 24576,
    supportsReasoningBudget: true,
    requiredReasoningBudget: true
  },
  "gemini-2.5-flash-preview-05-20": {
    maxTokens: 65535,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 0.15,
    outputPrice: 0.6,
    cacheReadsPrice: 0.0375,
    cacheWritesPrice: 1
  },
  "gemini-2.5-pro-exp-03-25": {
    maxTokens: 65535,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0
  },
  "gemini-2.5-pro-preview-03-25": {
    maxTokens: 65535,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 2.5,
    // This is the pricing for prompts above 200k tokens.
    outputPrice: 15,
    cacheReadsPrice: 0.625,
    cacheWritesPrice: 4.5,
    tiers: [
      {
        contextWindow: 2e5,
        inputPrice: 1.25,
        outputPrice: 10,
        cacheReadsPrice: 0.31
      },
      {
        contextWindow: Infinity,
        inputPrice: 2.5,
        outputPrice: 15,
        cacheReadsPrice: 0.625
      }
    ]
  },
  "gemini-2.5-pro-preview-05-06": {
    maxTokens: 65535,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 2.5,
    // This is the pricing for prompts above 200k tokens.
    outputPrice: 15,
    cacheReadsPrice: 0.625,
    cacheWritesPrice: 4.5,
    tiers: [
      {
        contextWindow: 2e5,
        inputPrice: 1.25,
        outputPrice: 10,
        cacheReadsPrice: 0.31
      },
      {
        contextWindow: Infinity,
        inputPrice: 2.5,
        outputPrice: 15,
        cacheReadsPrice: 0.625
      }
    ]
  },
  "gemini-2.0-flash-001": {
    maxTokens: 8192,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 0.1,
    outputPrice: 0.4,
    cacheReadsPrice: 0.025,
    cacheWritesPrice: 1
  },
  "gemini-2.0-flash-lite-preview-02-05": {
    maxTokens: 8192,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0
  },
  "gemini-2.0-pro-exp-02-05": {
    maxTokens: 8192,
    contextWindow: 2097152,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0
  },
  "gemini-2.0-flash-thinking-exp-01-21": {
    maxTokens: 65536,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0
  },
  "gemini-2.0-flash-thinking-exp-1219": {
    maxTokens: 8192,
    contextWindow: 32767,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0
  },
  "gemini-2.0-flash-exp": {
    maxTokens: 8192,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0
  },
  "gemini-1.5-flash-002": {
    maxTokens: 8192,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 0.15,
    // This is the pricing for prompts above 128k tokens.
    outputPrice: 0.6,
    cacheReadsPrice: 0.0375,
    cacheWritesPrice: 1,
    tiers: [
      {
        contextWindow: 128e3,
        inputPrice: 0.075,
        outputPrice: 0.3,
        cacheReadsPrice: 0.01875
      },
      {
        contextWindow: Infinity,
        inputPrice: 0.15,
        outputPrice: 0.6,
        cacheReadsPrice: 0.0375
      }
    ]
  },
  "gemini-1.5-flash-exp-0827": {
    maxTokens: 8192,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0
  },
  "gemini-1.5-flash-8b-exp-0827": {
    maxTokens: 8192,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0
  },
  "gemini-1.5-pro-002": {
    maxTokens: 8192,
    contextWindow: 2097152,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0
  },
  "gemini-1.5-pro-exp-0827": {
    maxTokens: 8192,
    contextWindow: 2097152,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0
  },
  "gemini-exp-1206": {
    maxTokens: 8192,
    contextWindow: 2097152,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0
  }
};

// src/providers/glama.ts
var glamaDefaultModelId = "anthropic/claude-3-7-sonnet";
var glamaDefaultModelInfo = {
  maxTokens: 8192,
  contextWindow: 2e5,
  supportsImages: true,
  supportsComputerUse: true,
  supportsPromptCache: true,
  inputPrice: 3,
  outputPrice: 15,
  cacheWritesPrice: 3.75,
  cacheReadsPrice: 0.3,
  description: "Claude 3.7 Sonnet is an advanced large language model with improved reasoning, coding, and problem-solving capabilities. It introduces a hybrid reasoning approach, allowing users to choose between rapid responses and extended, step-by-step processing for complex tasks. The model demonstrates notable improvements in coding, particularly in front-end development and full-stack updates, and excels in agentic workflows, where it can autonomously navigate multi-step processes. Claude 3.7 Sonnet maintains performance parity with its predecessor in standard mode while offering an extended reasoning mode for enhanced accuracy in math, coding, and instruction-following tasks. Read more at the [blog post here](https://www.anthropic.com/news/claude-3-7-sonnet)"
};
var GLAMA_DEFAULT_TEMPERATURE = 0;

// src/providers/groq.ts
var groqDefaultModelId = "llama-3.3-70b-versatile";
var groqModels = {
  // Models based on API response: https://api.groq.com/openai/v1/models
  "llama-3.1-8b-instant": {
    maxTokens: 131072,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "Meta Llama 3.1 8B Instant model, 128K context."
  },
  "llama-3.3-70b-versatile": {
    maxTokens: 32768,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "Meta Llama 3.3 70B Versatile model, 128K context."
  },
  "meta-llama/llama-4-scout-17b-16e-instruct": {
    maxTokens: 8192,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "Meta Llama 4 Scout 17B Instruct model, 128K context."
  },
  "meta-llama/llama-4-maverick-17b-128e-instruct": {
    maxTokens: 8192,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "Meta Llama 4 Maverick 17B Instruct model, 128K context."
  },
  "mistral-saba-24b": {
    maxTokens: 32768,
    contextWindow: 32768,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "Mistral Saba 24B model, 32K context."
  },
  "qwen-qwq-32b": {
    maxTokens: 131072,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "Alibaba Qwen QwQ 32B model, 128K context."
  },
  "deepseek-r1-distill-llama-70b": {
    maxTokens: 131072,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "DeepSeek R1 Distill Llama 70B model, 128K context."
  }
};

// src/providers/lite-llm.ts
var litellmDefaultModelId = "claude-3-7-sonnet-20250219";
var litellmDefaultModelInfo = {
  maxTokens: 8192,
  contextWindow: 2e5,
  supportsImages: true,
  supportsComputerUse: true,
  supportsPromptCache: true,
  inputPrice: 3,
  outputPrice: 15,
  cacheWritesPrice: 3.75,
  cacheReadsPrice: 0.3
};
var LITELLM_COMPUTER_USE_MODELS = /* @__PURE__ */ new Set([
  "claude-3-5-sonnet-latest",
  "claude-opus-4-20250514",
  "claude-sonnet-4-20250514",
  "claude-3-7-sonnet-latest",
  "claude-3-7-sonnet-20250219",
  "claude-3-5-sonnet-20241022",
  "vertex_ai/claude-3-5-sonnet",
  "vertex_ai/claude-3-5-sonnet-v2",
  "vertex_ai/claude-3-5-sonnet-v2@20241022",
  "vertex_ai/claude-3-7-sonnet@20250219",
  "vertex_ai/claude-opus-4@20250514",
  "vertex_ai/claude-sonnet-4@20250514",
  "openrouter/anthropic/claude-3.5-sonnet",
  "openrouter/anthropic/claude-3.5-sonnet:beta",
  "openrouter/anthropic/claude-3.7-sonnet",
  "openrouter/anthropic/claude-3.7-sonnet:beta",
  "anthropic.claude-opus-4-20250514-v1:0",
  "anthropic.claude-sonnet-4-20250514-v1:0",
  "anthropic.claude-3-7-sonnet-20250219-v1:0",
  "anthropic.claude-3-5-sonnet-20241022-v2:0",
  "us.anthropic.claude-3-5-sonnet-20241022-v2:0",
  "us.anthropic.claude-3-7-sonnet-20250219-v1:0",
  "us.anthropic.claude-opus-4-20250514-v1:0",
  "us.anthropic.claude-sonnet-4-20250514-v1:0",
  "eu.anthropic.claude-3-5-sonnet-20241022-v2:0",
  "eu.anthropic.claude-3-7-sonnet-20250219-v1:0",
  "eu.anthropic.claude-opus-4-20250514-v1:0",
  "eu.anthropic.claude-sonnet-4-20250514-v1:0",
  "snowflake/claude-3-5-sonnet"
]);

// src/providers/lm-studio.ts
var LMSTUDIO_DEFAULT_TEMPERATURE = 0;

// src/providers/mistral.ts
var mistralDefaultModelId = "codestral-latest";
var mistralModels = {
  "codestral-latest": {
    maxTokens: 256e3,
    contextWindow: 256e3,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0.3,
    outputPrice: 0.9
  },
  "mistral-large-latest": {
    maxTokens: 131e3,
    contextWindow: 131e3,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 2,
    outputPrice: 6
  },
  "ministral-8b-latest": {
    maxTokens: 131e3,
    contextWindow: 131e3,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0.1,
    outputPrice: 0.1
  },
  "ministral-3b-latest": {
    maxTokens: 131e3,
    contextWindow: 131e3,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0.04,
    outputPrice: 0.04
  },
  "mistral-small-latest": {
    maxTokens: 32e3,
    contextWindow: 32e3,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0.2,
    outputPrice: 0.6
  },
  "pixtral-large-latest": {
    maxTokens: 131e3,
    contextWindow: 131e3,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 2,
    outputPrice: 6
  }
};
var MISTRAL_DEFAULT_TEMPERATURE = 0;

// src/providers/openai.ts
var openAiNativeDefaultModelId = "gpt-4.1";
var openAiNativeModels = {
  "gpt-4.1": {
    maxTokens: 32768,
    contextWindow: 1047576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 2,
    outputPrice: 8,
    cacheReadsPrice: 0.5
  },
  "gpt-4.1-mini": {
    maxTokens: 32768,
    contextWindow: 1047576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 0.4,
    outputPrice: 1.6,
    cacheReadsPrice: 0.1
  },
  "gpt-4.1-nano": {
    maxTokens: 32768,
    contextWindow: 1047576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 0.1,
    outputPrice: 0.4,
    cacheReadsPrice: 0.025
  },
  o3: {
    maxTokens: 1e5,
    contextWindow: 2e5,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 10,
    outputPrice: 40,
    cacheReadsPrice: 2.5,
    supportsReasoningEffort: true,
    reasoningEffort: "medium"
  },
  "o3-high": {
    maxTokens: 1e5,
    contextWindow: 2e5,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 10,
    outputPrice: 40,
    cacheReadsPrice: 2.5,
    reasoningEffort: "high"
  },
  "o3-low": {
    maxTokens: 1e5,
    contextWindow: 2e5,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 10,
    outputPrice: 40,
    cacheReadsPrice: 2.5,
    reasoningEffort: "low"
  },
  "o4-mini": {
    maxTokens: 1e5,
    contextWindow: 2e5,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 1.1,
    outputPrice: 4.4,
    cacheReadsPrice: 0.275,
    supportsReasoningEffort: true,
    reasoningEffort: "medium"
  },
  "o4-mini-high": {
    maxTokens: 1e5,
    contextWindow: 2e5,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 1.1,
    outputPrice: 4.4,
    cacheReadsPrice: 0.275,
    reasoningEffort: "high"
  },
  "o4-mini-low": {
    maxTokens: 1e5,
    contextWindow: 2e5,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 1.1,
    outputPrice: 4.4,
    cacheReadsPrice: 0.275,
    reasoningEffort: "low"
  },
  "o3-mini": {
    maxTokens: 1e5,
    contextWindow: 2e5,
    supportsImages: false,
    supportsPromptCache: true,
    inputPrice: 1.1,
    outputPrice: 4.4,
    cacheReadsPrice: 0.55,
    supportsReasoningEffort: true,
    reasoningEffort: "medium"
  },
  "o3-mini-high": {
    maxTokens: 1e5,
    contextWindow: 2e5,
    supportsImages: false,
    supportsPromptCache: true,
    inputPrice: 1.1,
    outputPrice: 4.4,
    cacheReadsPrice: 0.55,
    reasoningEffort: "high"
  },
  "o3-mini-low": {
    maxTokens: 1e5,
    contextWindow: 2e5,
    supportsImages: false,
    supportsPromptCache: true,
    inputPrice: 1.1,
    outputPrice: 4.4,
    cacheReadsPrice: 0.55,
    reasoningEffort: "low"
  },
  o1: {
    maxTokens: 1e5,
    contextWindow: 2e5,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 15,
    outputPrice: 60,
    cacheReadsPrice: 7.5
  },
  "o1-preview": {
    maxTokens: 32768,
    contextWindow: 128e3,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 15,
    outputPrice: 60,
    cacheReadsPrice: 7.5
  },
  "o1-mini": {
    maxTokens: 65536,
    contextWindow: 128e3,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 1.1,
    outputPrice: 4.4,
    cacheReadsPrice: 0.55
  },
  "gpt-4.5-preview": {
    maxTokens: 16384,
    contextWindow: 128e3,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 75,
    outputPrice: 150,
    cacheReadsPrice: 37.5
  },
  "gpt-4o": {
    maxTokens: 16384,
    contextWindow: 128e3,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 2.5,
    outputPrice: 10,
    cacheReadsPrice: 1.25
  },
  "gpt-4o-mini": {
    maxTokens: 16384,
    contextWindow: 128e3,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 0.15,
    outputPrice: 0.6,
    cacheReadsPrice: 0.075
  }
};
var openAiModelInfoSaneDefaults = {
  maxTokens: -1,
  contextWindow: 128e3,
  supportsImages: true,
  supportsPromptCache: false,
  inputPrice: 0,
  outputPrice: 0
};
var azureOpenAiDefaultApiVersion = "2024-08-01-preview";
var OPENAI_NATIVE_DEFAULT_TEMPERATURE = 0;
var OPENAI_AZURE_AI_INFERENCE_PATH = "/models/chat/completions";

// src/providers/openrouter.ts
var openRouterDefaultModelId = "anthropic/claude-sonnet-4";
var openRouterDefaultModelInfo = {
  maxTokens: 8192,
  contextWindow: 2e5,
  supportsImages: true,
  supportsComputerUse: true,
  supportsPromptCache: true,
  inputPrice: 3,
  outputPrice: 15,
  cacheWritesPrice: 3.75,
  cacheReadsPrice: 0.3,
  description: "Claude 3.7 Sonnet is an advanced large language model with improved reasoning, coding, and problem-solving capabilities. It introduces a hybrid reasoning approach, allowing users to choose between rapid responses and extended, step-by-step processing for complex tasks. The model demonstrates notable improvements in coding, particularly in front-end development and full-stack updates, and excels in agentic workflows, where it can autonomously navigate multi-step processes. Claude 3.7 Sonnet maintains performance parity with its predecessor in standard mode while offering an extended reasoning mode for enhanced accuracy in math, coding, and instruction-following tasks. Read more at the [blog post here](https://www.anthropic.com/news/claude-3-7-sonnet)"
};
var OPENROUTER_DEFAULT_PROVIDER_NAME = "[default]";
var OPEN_ROUTER_PROMPT_CACHING_MODELS = /* @__PURE__ */ new Set([
  "anthropic/claude-3-haiku",
  "anthropic/claude-3-haiku:beta",
  "anthropic/claude-3-opus",
  "anthropic/claude-3-opus:beta",
  "anthropic/claude-3-sonnet",
  "anthropic/claude-3-sonnet:beta",
  "anthropic/claude-3.5-haiku",
  "anthropic/claude-3.5-haiku-20241022",
  "anthropic/claude-3.5-haiku-20241022:beta",
  "anthropic/claude-3.5-haiku:beta",
  "anthropic/claude-3.5-sonnet",
  "anthropic/claude-3.5-sonnet-20240620",
  "anthropic/claude-3.5-sonnet-20240620:beta",
  "anthropic/claude-3.5-sonnet:beta",
  "anthropic/claude-3.7-sonnet",
  "anthropic/claude-3.7-sonnet:beta",
  "anthropic/claude-3.7-sonnet:thinking",
  "anthropic/claude-sonnet-4",
  "anthropic/claude-opus-4",
  "google/gemini-2.5-pro-preview",
  "google/gemini-2.5-flash-preview",
  "google/gemini-2.5-flash-preview:thinking",
  "google/gemini-2.5-flash-preview-05-20",
  "google/gemini-2.5-flash-preview-05-20:thinking",
  "google/gemini-2.0-flash-001",
  "google/gemini-flash-1.5",
  "google/gemini-flash-1.5-8b"
]);
var OPEN_ROUTER_COMPUTER_USE_MODELS = /* @__PURE__ */ new Set([
  "anthropic/claude-3.5-sonnet",
  "anthropic/claude-3.5-sonnet:beta",
  "anthropic/claude-3.7-sonnet",
  "anthropic/claude-3.7-sonnet:beta",
  "anthropic/claude-3.7-sonnet:thinking",
  "anthropic/claude-sonnet-4",
  "anthropic/claude-opus-4"
]);
var OPEN_ROUTER_REASONING_BUDGET_MODELS = /* @__PURE__ */ new Set([
  "anthropic/claude-3.7-sonnet:beta",
  "anthropic/claude-3.7-sonnet:thinking",
  "anthropic/claude-opus-4",
  "anthropic/claude-sonnet-4",
  "google/gemini-2.5-flash-preview-05-20",
  "google/gemini-2.5-flash-preview-05-20:thinking"
]);
var OPEN_ROUTER_REQUIRED_REASONING_BUDGET_MODELS = /* @__PURE__ */ new Set([
  "anthropic/claude-3.7-sonnet:thinking",
  "google/gemini-2.5-flash-preview-05-20:thinking"
]);

// src/providers/requesty.ts
var requestyDefaultModelId = "coding/claude-4-sonnet";
var requestyDefaultModelInfo = {
  maxTokens: 8192,
  contextWindow: 2e5,
  supportsImages: true,
  supportsComputerUse: true,
  supportsPromptCache: true,
  inputPrice: 3,
  outputPrice: 15,
  cacheWritesPrice: 3.75,
  cacheReadsPrice: 0.3,
  description: "The best coding model, optimized by Requesty, and automatically routed to the fastest provider. Claude 4 Sonnet is an advanced large language model with improved reasoning, coding, and problem-solving capabilities."
};

// src/providers/unbound.ts
var unboundDefaultModelId = "anthropic/claude-3-7-sonnet-20250219";
var unboundDefaultModelInfo = {
  maxTokens: 8192,
  contextWindow: 2e5,
  supportsImages: true,
  supportsPromptCache: true,
  inputPrice: 3,
  outputPrice: 15,
  cacheWritesPrice: 3.75,
  cacheReadsPrice: 0.3
};

// src/providers/vertex.ts
var vertexDefaultModelId = "claude-sonnet-4@20250514";
var vertexModels = {
  "gemini-2.5-flash-preview-05-20:thinking": {
    maxTokens: 65535,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 0.15,
    outputPrice: 3.5,
    maxThinkingTokens: 24576,
    supportsReasoningBudget: true,
    requiredReasoningBudget: true
  },
  "gemini-2.5-flash-preview-05-20": {
    maxTokens: 65535,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 0.15,
    outputPrice: 0.6
  },
  "gemini-2.5-flash-preview-04-17:thinking": {
    maxTokens: 65535,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0.15,
    outputPrice: 3.5,
    maxThinkingTokens: 24576,
    supportsReasoningBudget: true,
    requiredReasoningBudget: true
  },
  "gemini-2.5-flash-preview-04-17": {
    maxTokens: 65535,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0.15,
    outputPrice: 0.6
  },
  "gemini-2.5-pro-preview-03-25": {
    maxTokens: 65535,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 2.5,
    outputPrice: 15
  },
  "gemini-2.5-pro-preview-05-06": {
    maxTokens: 65535,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 2.5,
    outputPrice: 15
  },
  "gemini-2.5-pro-exp-03-25": {
    maxTokens: 65535,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0
  },
  "gemini-2.0-pro-exp-02-05": {
    maxTokens: 8192,
    contextWindow: 2097152,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0
  },
  "gemini-2.0-flash-001": {
    maxTokens: 8192,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 0.15,
    outputPrice: 0.6
  },
  "gemini-2.0-flash-lite-001": {
    maxTokens: 8192,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0.075,
    outputPrice: 0.3
  },
  "gemini-2.0-flash-thinking-exp-01-21": {
    maxTokens: 8192,
    contextWindow: 32768,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0
  },
  "gemini-1.5-flash-002": {
    maxTokens: 8192,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 0.075,
    outputPrice: 0.3
  },
  "gemini-1.5-pro-002": {
    maxTokens: 8192,
    contextWindow: 2097152,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 1.25,
    outputPrice: 5
  },
  "claude-sonnet-4@20250514": {
    maxTokens: 8192,
    contextWindow: 2e5,
    supportsImages: true,
    supportsComputerUse: true,
    supportsPromptCache: true,
    inputPrice: 3,
    outputPrice: 15,
    cacheWritesPrice: 3.75,
    cacheReadsPrice: 0.3,
    supportsReasoningBudget: true
  },
  "claude-opus-4@20250514": {
    maxTokens: 8192,
    contextWindow: 2e5,
    supportsImages: true,
    supportsComputerUse: true,
    supportsPromptCache: true,
    inputPrice: 15,
    outputPrice: 75,
    cacheWritesPrice: 18.75,
    cacheReadsPrice: 1.5
  },
  "claude-3-7-sonnet@20250219:thinking": {
    maxTokens: 64e3,
    contextWindow: 2e5,
    supportsImages: true,
    supportsComputerUse: true,
    supportsPromptCache: true,
    inputPrice: 3,
    outputPrice: 15,
    cacheWritesPrice: 3.75,
    cacheReadsPrice: 0.3,
    supportsReasoningBudget: true,
    requiredReasoningBudget: true
  },
  "claude-3-7-sonnet@20250219": {
    maxTokens: 8192,
    contextWindow: 2e5,
    supportsImages: true,
    supportsComputerUse: true,
    supportsPromptCache: true,
    inputPrice: 3,
    outputPrice: 15,
    cacheWritesPrice: 3.75,
    cacheReadsPrice: 0.3
  },
  "claude-3-5-sonnet-v2@20241022": {
    maxTokens: 8192,
    contextWindow: 2e5,
    supportsImages: true,
    supportsComputerUse: true,
    supportsPromptCache: true,
    inputPrice: 3,
    outputPrice: 15,
    cacheWritesPrice: 3.75,
    cacheReadsPrice: 0.3
  },
  "claude-3-5-sonnet@20240620": {
    maxTokens: 8192,
    contextWindow: 2e5,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 3,
    outputPrice: 15,
    cacheWritesPrice: 3.75,
    cacheReadsPrice: 0.3
  },
  "claude-3-5-haiku@20241022": {
    maxTokens: 8192,
    contextWindow: 2e5,
    supportsImages: false,
    supportsPromptCache: true,
    inputPrice: 1,
    outputPrice: 5,
    cacheWritesPrice: 1.25,
    cacheReadsPrice: 0.1
  },
  "claude-3-opus@20240229": {
    maxTokens: 4096,
    contextWindow: 2e5,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 15,
    outputPrice: 75,
    cacheWritesPrice: 18.75,
    cacheReadsPrice: 1.5
  },
  "claude-3-haiku@20240307": {
    maxTokens: 4096,
    contextWindow: 2e5,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 0.25,
    outputPrice: 1.25,
    cacheWritesPrice: 0.3,
    cacheReadsPrice: 0.03
  }
};
var VERTEX_REGIONS = [
  { value: "us-east5", label: "us-east5" },
  { value: "us-central1", label: "us-central1" },
  { value: "europe-west1", label: "europe-west1" },
  { value: "europe-west4", label: "europe-west4" },
  { value: "asia-southeast1", label: "asia-southeast1" }
];

// src/providers/vscode-llm.ts
var vscodeLlmDefaultModelId = "claude-3.5-sonnet";
var vscodeLlmModels = {
  "gpt-3.5-turbo": {
    contextWindow: 12114,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    family: "gpt-3.5-turbo",
    version: "gpt-3.5-turbo-0613",
    name: "GPT 3.5 Turbo",
    supportsToolCalling: true,
    maxInputTokens: 12114
  },
  "gpt-4o-mini": {
    contextWindow: 12115,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    family: "gpt-4o-mini",
    version: "gpt-4o-mini-2024-07-18",
    name: "GPT-4o mini",
    supportsToolCalling: true,
    maxInputTokens: 12115
  },
  "gpt-4": {
    contextWindow: 28501,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    family: "gpt-4",
    version: "gpt-4-0613",
    name: "GPT 4",
    supportsToolCalling: true,
    maxInputTokens: 28501
  },
  "gpt-4-0125-preview": {
    contextWindow: 63826,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    family: "gpt-4-turbo",
    version: "gpt-4-0125-preview",
    name: "GPT 4 Turbo",
    supportsToolCalling: true,
    maxInputTokens: 63826
  },
  "gpt-4o": {
    contextWindow: 63827,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    family: "gpt-4o",
    version: "gpt-4o-2024-11-20",
    name: "GPT-4o",
    supportsToolCalling: true,
    maxInputTokens: 63827
  },
  o1: {
    contextWindow: 19827,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    family: "o1-ga",
    version: "o1-2024-12-17",
    name: "o1 (Preview)",
    supportsToolCalling: true,
    maxInputTokens: 19827
  },
  "o3-mini": {
    contextWindow: 63827,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    family: "o3-mini",
    version: "o3-mini-2025-01-31",
    name: "o3-mini",
    supportsToolCalling: true,
    maxInputTokens: 63827
  },
  "claude-3.5-sonnet": {
    contextWindow: 81638,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    family: "claude-3.5-sonnet",
    version: "claude-3.5-sonnet",
    name: "Claude 3.5 Sonnet",
    supportsToolCalling: true,
    maxInputTokens: 81638
  },
  "gemini-2.0-flash-001": {
    contextWindow: 127827,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    family: "gemini-2.0-flash",
    version: "gemini-2.0-flash-001",
    name: "Gemini 2.0 Flash",
    supportsToolCalling: false,
    maxInputTokens: 127827
  },
  "gemini-2.5-pro": {
    contextWindow: 63830,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    family: "gemini-2.5-pro",
    version: "gemini-2.5-pro-preview-03-25",
    name: "Gemini 2.5 Pro (Preview)",
    supportsToolCalling: true,
    maxInputTokens: 63830
  },
  "o4-mini": {
    contextWindow: 111446,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    family: "o4-mini",
    version: "o4-mini-2025-04-16",
    name: "o4-mini (Preview)",
    supportsToolCalling: true,
    maxInputTokens: 111446
  },
  "gpt-4.1": {
    contextWindow: 111446,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    family: "gpt-4.1",
    version: "gpt-4.1-2025-04-14",
    name: "GPT-4.1 (Preview)",
    supportsToolCalling: true,
    maxInputTokens: 111446
  }
};

// src/providers/xai.ts
var xaiDefaultModelId = "grok-3-mini";
var xaiModels = {
  "grok-3-beta": {
    maxTokens: 8192,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 3,
    outputPrice: 15,
    description: "xAI's Grok-3 beta model with 131K context window"
  },
  "grok-3-fast-beta": {
    maxTokens: 8192,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 5,
    outputPrice: 25,
    description: "xAI's Grok-3 fast beta model with 131K context window"
  },
  "grok-3-mini-beta": {
    maxTokens: 8192,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0.3,
    outputPrice: 0.5,
    description: "xAI's Grok-3 mini beta model with 131K context window",
    supportsReasoningEffort: true
  },
  "grok-3-mini-fast-beta": {
    maxTokens: 8192,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0.6,
    outputPrice: 4,
    description: "xAI's Grok-3 mini fast beta model with 131K context window",
    supportsReasoningEffort: true
  },
  "grok-3": {
    maxTokens: 8192,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 3,
    outputPrice: 15,
    description: "xAI's Grok-3 model with 131K context window"
  },
  "grok-3-fast": {
    maxTokens: 8192,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 5,
    outputPrice: 25,
    description: "xAI's Grok-3 fast model with 131K context window"
  },
  "grok-3-mini": {
    maxTokens: 8192,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0.3,
    outputPrice: 0.5,
    description: "xAI's Grok-3 mini model with 131K context window",
    supportsReasoningEffort: true
  },
  "grok-3-mini-fast": {
    maxTokens: 8192,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0.6,
    outputPrice: 4,
    description: "xAI's Grok-3 mini fast model with 131K context window",
    supportsReasoningEffort: true
  },
  "grok-2-latest": {
    maxTokens: 8192,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 2,
    outputPrice: 10,
    description: "xAI's Grok-2 model - latest version with 131K context window"
  },
  "grok-2": {
    maxTokens: 8192,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 2,
    outputPrice: 10,
    description: "xAI's Grok-2 model with 131K context window"
  },
  "grok-2-1212": {
    maxTokens: 8192,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 2,
    outputPrice: 10,
    description: "xAI's Grok-2 model (version 1212) with 131K context window"
  },
  "grok-2-vision-latest": {
    maxTokens: 8192,
    contextWindow: 32768,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 2,
    outputPrice: 10,
    description: "xAI's Grok-2 Vision model - latest version with image support and 32K context window"
  },
  "grok-2-vision": {
    maxTokens: 8192,
    contextWindow: 32768,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 2,
    outputPrice: 10,
    description: "xAI's Grok-2 Vision model with image support and 32K context window"
  },
  "grok-2-vision-1212": {
    maxTokens: 8192,
    contextWindow: 32768,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 2,
    outputPrice: 10,
    description: "xAI's Grok-2 Vision model (version 1212) with image support and 32K context window"
  },
  "grok-vision-beta": {
    maxTokens: 8192,
    contextWindow: 8192,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 5,
    outputPrice: 15,
    description: "xAI's Grok Vision Beta model with image support and 8K context window"
  },
  "grok-beta": {
    maxTokens: 8192,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 5,
    outputPrice: 15,
    description: "xAI's Grok Beta model (legacy) with 131K context window"
  }
};

// src/codebase-index.ts
import { z } from "zod";
var codebaseIndexConfigSchema = z.object({
  codebaseIndexEnabled: z.boolean().optional(),
  codebaseIndexQdrantUrl: z.string().optional(),
  codebaseIndexEmbedderProvider: z.enum(["openai", "ollama"]).optional(),
  codebaseIndexEmbedderBaseUrl: z.string().optional(),
  codebaseIndexEmbedderModelId: z.string().optional()
});
var codebaseIndexModelsSchema = z.object({
  openai: z.record(z.string(), z.object({ dimension: z.number() })).optional(),
  ollama: z.record(z.string(), z.object({ dimension: z.number() })).optional()
});
var codebaseIndexProviderSchema = z.object({
  codeIndexOpenAiKey: z.string().optional(),
  codeIndexQdrantApiKey: z.string().optional()
});

// src/cloud.ts
import { z as z2 } from "zod";
var organizationAllowListSchema = z2.object({
  allowAll: z2.boolean(),
  providers: z2.record(
    z2.object({
      allowAll: z2.boolean(),
      models: z2.array(z2.string()).optional()
    })
  )
});
var ORGANIZATION_ALLOW_ALL = {
  allowAll: true,
  providers: {}
};
var organizationSettingsSchema = z2.object({
  version: z2.number(),
  defaultSettings: z2.object({
    enableCheckpoints: z2.boolean().optional(),
    maxOpenTabsContext: z2.number().optional(),
    maxWorkspaceFiles: z2.number().optional(),
    showRooIgnoredFiles: z2.boolean().optional(),
    maxReadFileLine: z2.number().optional(),
    fuzzyMatchThreshold: z2.number().optional()
  }).optional(),
  cloudSettings: z2.object({
    recordTaskMessages: z2.boolean().optional()
  }).optional(),
  allowList: organizationAllowListSchema
});

// src/experiment.ts
import { z as z3 } from "zod";
var experimentIds = ["powerSteering", "concurrentFileReads"];
var experimentIdsSchema = z3.enum(experimentIds);
var experimentsSchema = z3.object({
  powerSteering: z3.boolean(),
  concurrentFileReads: z3.boolean()
});

// src/global-settings.ts
import { z as z12 } from "zod";

// src/type-fu.ts
function keysOf() {
  return (keys) => keys;
}

// src/provider-settings.ts
import { z as z5 } from "zod";

// src/model.ts
import { z as z4 } from "zod";
var reasoningEfforts = ["low", "medium", "high"];
var reasoningEffortsSchema = z4.enum(reasoningEfforts);
var modelParameters = ["max_tokens", "temperature", "reasoning", "include_reasoning"];
var modelParametersSchema = z4.enum(modelParameters);
var isModelParameter = (value) => modelParameters.includes(value);
var modelInfoSchema = z4.object({
  maxTokens: z4.number().nullish(),
  maxThinkingTokens: z4.number().nullish(),
  contextWindow: z4.number(),
  supportsImages: z4.boolean().optional(),
  supportsComputerUse: z4.boolean().optional(),
  supportsPromptCache: z4.boolean(),
  supportsReasoningBudget: z4.boolean().optional(),
  requiredReasoningBudget: z4.boolean().optional(),
  supportsReasoningEffort: z4.boolean().optional(),
  supportedParameters: z4.array(modelParametersSchema).optional(),
  inputPrice: z4.number().optional(),
  outputPrice: z4.number().optional(),
  cacheWritesPrice: z4.number().optional(),
  cacheReadsPrice: z4.number().optional(),
  description: z4.string().optional(),
  reasoningEffort: reasoningEffortsSchema.optional(),
  minTokensPerCachePoint: z4.number().optional(),
  maxCachePoints: z4.number().optional(),
  cachableFields: z4.array(z4.string()).optional(),
  tiers: z4.array(
    z4.object({
      contextWindow: z4.number(),
      inputPrice: z4.number().optional(),
      outputPrice: z4.number().optional(),
      cacheWritesPrice: z4.number().optional(),
      cacheReadsPrice: z4.number().optional()
    })
  ).optional()
});

// src/provider-settings.ts
var providerNames = [
  "anthropic",
  "glama",
  "openrouter",
  "bedrock",
  "vertex",
  "openai",
  "ollama",
  "vscode-lm",
  "lmstudio",
  "gemini",
  "openai-native",
  "mistral",
  "deepseek",
  "unbound",
  "requesty",
  "human-relay",
  "fake-ai",
  "xai",
  "groq",
  "chutes",
  "litellm"
];
var providerNamesSchema = z5.enum(providerNames);
var providerSettingsEntrySchema = z5.object({
  id: z5.string(),
  name: z5.string(),
  apiProvider: providerNamesSchema.optional()
});
var baseProviderSettingsSchema = z5.object({
  includeMaxTokens: z5.boolean().optional(),
  diffEnabled: z5.boolean().optional(),
  fuzzyMatchThreshold: z5.number().optional(),
  modelTemperature: z5.number().nullish(),
  rateLimitSeconds: z5.number().optional(),
  // Model reasoning.
  enableReasoningEffort: z5.boolean().optional(),
  reasoningEffort: reasoningEffortsSchema.optional(),
  modelMaxTokens: z5.number().optional(),
  modelMaxThinkingTokens: z5.number().optional()
});
var apiModelIdProviderModelSchema = baseProviderSettingsSchema.extend({
  apiModelId: z5.string().optional()
});
var anthropicSchema = apiModelIdProviderModelSchema.extend({
  apiKey: z5.string().optional(),
  // Keep for backward compatibility
  anthropicApiKey: z5.string().optional(),
  anthropicBaseUrl: z5.string().optional(),
  anthropicUseAuthToken: z5.boolean().optional()
});
var glamaSchema = baseProviderSettingsSchema.extend({
  glamaModelId: z5.string().optional(),
  glamaApiKey: z5.string().optional()
});
var openRouterSchema = baseProviderSettingsSchema.extend({
  openRouterApiKey: z5.string().optional(),
  openRouterModelId: z5.string().optional(),
  openRouterBaseUrl: z5.string().optional(),
  openRouterSpecificProvider: z5.string().optional(),
  openRouterUseMiddleOutTransform: z5.boolean().optional()
});
var bedrockSchema = apiModelIdProviderModelSchema.extend({
  awsAccessKey: z5.string().optional(),
  awsSecretKey: z5.string().optional(),
  awsSessionToken: z5.string().optional(),
  awsRegion: z5.string().optional(),
  awsUseCrossRegionInference: z5.boolean().optional(),
  awsUsePromptCache: z5.boolean().optional(),
  awsProfile: z5.string().optional(),
  awsUseProfile: z5.boolean().optional(),
  awsCustomArn: z5.string().optional(),
  awsBedrockEndpointEnabled: z5.boolean().optional(),
  awsBedrockEndpoint: z5.string().optional()
});
var vertexSchema = apiModelIdProviderModelSchema.extend({
  vertexKeyFile: z5.string().optional(),
  vertexJsonCredentials: z5.string().optional(),
  vertexProjectId: z5.string().optional(),
  vertexRegion: z5.string().optional()
});
var openAiSchema = baseProviderSettingsSchema.extend({
  openAiBaseUrl: z5.string().optional(),
  openAiApiKey: z5.string().optional(),
  openAiLegacyFormat: z5.boolean().optional(),
  openAiR1FormatEnabled: z5.boolean().optional(),
  openAiModelId: z5.string().optional(),
  openAiCustomModelInfo: modelInfoSchema.nullish(),
  openAiUseAzure: z5.boolean().optional(),
  azureApiVersion: z5.string().optional(),
  openAiStreamingEnabled: z5.boolean().optional(),
  openAiHostHeader: z5.string().optional(),
  // Keep temporarily for backward compatibility during migration.
  openAiHeaders: z5.record(z5.string(), z5.string()).optional()
});
var ollamaSchema = baseProviderSettingsSchema.extend({
  ollamaModelId: z5.string().optional(),
  ollamaBaseUrl: z5.string().optional()
});
var vsCodeLmSchema = baseProviderSettingsSchema.extend({
  vsCodeLmModelSelector: z5.object({
    vendor: z5.string().optional(),
    family: z5.string().optional(),
    version: z5.string().optional(),
    id: z5.string().optional()
  }).optional()
});
var lmStudioSchema = baseProviderSettingsSchema.extend({
  lmStudioModelId: z5.string().optional(),
  lmStudioBaseUrl: z5.string().optional(),
  lmStudioDraftModelId: z5.string().optional(),
  lmStudioSpeculativeDecodingEnabled: z5.boolean().optional()
});
var geminiSchema = apiModelIdProviderModelSchema.extend({
  geminiApiKey: z5.string().optional(),
  googleGeminiBaseUrl: z5.string().optional()
});
var openAiNativeSchema = apiModelIdProviderModelSchema.extend({
  openAiNativeApiKey: z5.string().optional(),
  openAiNativeBaseUrl: z5.string().optional()
});
var mistralSchema = apiModelIdProviderModelSchema.extend({
  mistralApiKey: z5.string().optional(),
  mistralCodestralUrl: z5.string().optional()
});
var deepSeekSchema = apiModelIdProviderModelSchema.extend({
  deepSeekBaseUrl: z5.string().optional(),
  deepSeekApiKey: z5.string().optional()
});
var unboundSchema = baseProviderSettingsSchema.extend({
  unboundApiKey: z5.string().optional(),
  unboundModelId: z5.string().optional()
});
var requestySchema = baseProviderSettingsSchema.extend({
  requestyApiKey: z5.string().optional(),
  requestyModelId: z5.string().optional()
});
var humanRelaySchema = baseProviderSettingsSchema;
var fakeAiSchema = baseProviderSettingsSchema.extend({
  fakeAi: z5.unknown().optional()
});
var xaiSchema = apiModelIdProviderModelSchema.extend({
  xaiApiKey: z5.string().optional()
});
var groqSchema = apiModelIdProviderModelSchema.extend({
  groqApiKey: z5.string().optional()
});
var chutesSchema = apiModelIdProviderModelSchema.extend({
  chutesApiKey: z5.string().optional()
});
var litellmSchema = baseProviderSettingsSchema.extend({
  litellmBaseUrl: z5.string().optional(),
  litellmApiKey: z5.string().optional(),
  litellmModelId: z5.string().optional()
});
var defaultSchema = z5.object({
  apiProvider: z5.undefined()
});
var providerSettingsSchemaDiscriminated = z5.discriminatedUnion("apiProvider", [
  anthropicSchema.merge(z5.object({ apiProvider: z5.literal("anthropic") })),
  glamaSchema.merge(z5.object({ apiProvider: z5.literal("glama") })),
  openRouterSchema.merge(z5.object({ apiProvider: z5.literal("openrouter") })),
  bedrockSchema.merge(z5.object({ apiProvider: z5.literal("bedrock") })),
  vertexSchema.merge(z5.object({ apiProvider: z5.literal("vertex") })),
  openAiSchema.merge(z5.object({ apiProvider: z5.literal("openai") })),
  ollamaSchema.merge(z5.object({ apiProvider: z5.literal("ollama") })),
  vsCodeLmSchema.merge(z5.object({ apiProvider: z5.literal("vscode-lm") })),
  lmStudioSchema.merge(z5.object({ apiProvider: z5.literal("lmstudio") })),
  geminiSchema.merge(z5.object({ apiProvider: z5.literal("gemini") })),
  openAiNativeSchema.merge(z5.object({ apiProvider: z5.literal("openai-native") })),
  mistralSchema.merge(z5.object({ apiProvider: z5.literal("mistral") })),
  deepSeekSchema.merge(z5.object({ apiProvider: z5.literal("deepseek") })),
  unboundSchema.merge(z5.object({ apiProvider: z5.literal("unbound") })),
  requestySchema.merge(z5.object({ apiProvider: z5.literal("requesty") })),
  humanRelaySchema.merge(z5.object({ apiProvider: z5.literal("human-relay") })),
  fakeAiSchema.merge(z5.object({ apiProvider: z5.literal("fake-ai") })),
  xaiSchema.merge(z5.object({ apiProvider: z5.literal("xai") })),
  groqSchema.merge(z5.object({ apiProvider: z5.literal("groq") })),
  chutesSchema.merge(z5.object({ apiProvider: z5.literal("chutes") })),
  litellmSchema.merge(z5.object({ apiProvider: z5.literal("litellm") })),
  defaultSchema
]);
var providerSettingsSchema = z5.object({
  apiProvider: providerNamesSchema.optional(),
  ...anthropicSchema.shape,
  ...glamaSchema.shape,
  ...openRouterSchema.shape,
  ...bedrockSchema.shape,
  ...vertexSchema.shape,
  ...openAiSchema.shape,
  ...ollamaSchema.shape,
  ...vsCodeLmSchema.shape,
  ...lmStudioSchema.shape,
  ...geminiSchema.shape,
  ...openAiNativeSchema.shape,
  ...mistralSchema.shape,
  ...deepSeekSchema.shape,
  ...unboundSchema.shape,
  ...requestySchema.shape,
  ...humanRelaySchema.shape,
  ...fakeAiSchema.shape,
  ...xaiSchema.shape,
  ...groqSchema.shape,
  ...chutesSchema.shape,
  ...litellmSchema.shape,
  ...codebaseIndexProviderSchema.shape
});
var PROVIDER_SETTINGS_KEYS = keysOf()([
  "apiProvider",
  // Anthropic
  "apiModelId",
  "apiKey",
  // Keep for backward compatibility
  "anthropicApiKey",
  "anthropicBaseUrl",
  "anthropicUseAuthToken",
  // Glama
  "glamaModelId",
  "glamaApiKey",
  // OpenRouter
  "openRouterApiKey",
  "openRouterModelId",
  "openRouterBaseUrl",
  "openRouterSpecificProvider",
  "openRouterUseMiddleOutTransform",
  // Amazon Bedrock
  "awsAccessKey",
  "awsSecretKey",
  "awsSessionToken",
  "awsRegion",
  "awsUseCrossRegionInference",
  "awsUsePromptCache",
  "awsProfile",
  "awsUseProfile",
  "awsCustomArn",
  "awsBedrockEndpointEnabled",
  "awsBedrockEndpoint",
  // Google Vertex
  "vertexKeyFile",
  "vertexJsonCredentials",
  "vertexProjectId",
  "vertexRegion",
  // OpenAI
  "openAiBaseUrl",
  "openAiApiKey",
  "openAiLegacyFormat",
  "openAiR1FormatEnabled",
  "openAiModelId",
  "openAiCustomModelInfo",
  "openAiUseAzure",
  "azureApiVersion",
  "openAiStreamingEnabled",
  "openAiHostHeader",
  // Keep temporarily for backward compatibility during migration.
  "openAiHeaders",
  // Ollama
  "ollamaModelId",
  "ollamaBaseUrl",
  // VS Code LM
  "vsCodeLmModelSelector",
  "lmStudioModelId",
  "lmStudioBaseUrl",
  "lmStudioDraftModelId",
  "lmStudioSpeculativeDecodingEnabled",
  // Gemini
  "geminiApiKey",
  "googleGeminiBaseUrl",
  // OpenAI Native
  "openAiNativeApiKey",
  "openAiNativeBaseUrl",
  // Mistral
  "mistralApiKey",
  "mistralCodestralUrl",
  // DeepSeek
  "deepSeekBaseUrl",
  "deepSeekApiKey",
  // Unbound
  "unboundApiKey",
  "unboundModelId",
  // Requesty
  "requestyApiKey",
  "requestyModelId",
  // Code Index
  "codeIndexOpenAiKey",
  "codeIndexQdrantApiKey",
  // Reasoning
  "enableReasoningEffort",
  "reasoningEffort",
  "modelMaxTokens",
  "modelMaxThinkingTokens",
  // Generic
  "includeMaxTokens",
  "diffEnabled",
  "fuzzyMatchThreshold",
  "modelTemperature",
  "rateLimitSeconds",
  // Fake AI
  "fakeAi",
  // X.AI (Grok)
  "xaiApiKey",
  // Groq
  "groqApiKey",
  // Chutes AI
  "chutesApiKey",
  // LiteLLM
  "litellmBaseUrl",
  "litellmApiKey",
  "litellmModelId"
]);

// src/history.ts
import { z as z6 } from "zod";
var historyItemSchema = z6.object({
  id: z6.string(),
  number: z6.number(),
  ts: z6.number(),
  task: z6.string(),
  tokensIn: z6.number(),
  tokensOut: z6.number(),
  cacheWrites: z6.number().optional(),
  cacheReads: z6.number().optional(),
  totalCost: z6.number(),
  size: z6.number().optional(),
  workspace: z6.string().optional(),
  title: z6.string().optional(),
  pinned: z6.boolean().optional()
});

// src/telemetry.ts
import { z as z8 } from "zod";

// src/message.ts
import { z as z7 } from "zod";
var clineAsks = [
  "followup",
  "command",
  "command_output",
  "completion_result",
  "tool",
  "api_req_failed",
  "resume_task",
  "resume_completed_task",
  "mistake_limit_reached",
  "browser_action_launch",
  "use_mcp_server",
  "auto_approval_max_req_reached"
];
var clineAskSchema = z7.enum(clineAsks);
var clineSays = [
  "error",
  "api_req_started",
  "api_req_finished",
  "api_req_retried",
  "api_req_retry_delayed",
  "api_req_deleted",
  "text",
  "reasoning",
  "completion_result",
  "user_feedback",
  "user_feedback_diff",
  "command_output",
  "shell_integration_warning",
  "browser_action",
  "browser_action_result",
  "mcp_server_request_started",
  "mcp_server_response",
  "subtask_result",
  "checkpoint_saved",
  "rooignore_error",
  "diff_error",
  "condense_context",
  "condense_context_error",
  "codebase_search_result"
];
var clineSaySchema = z7.enum(clineSays);
var toolProgressStatusSchema = z7.object({
  icon: z7.string().optional(),
  text: z7.string().optional()
});
var contextCondenseSchema = z7.object({
  cost: z7.number(),
  prevContextTokens: z7.number(),
  newContextTokens: z7.number(),
  summary: z7.string()
});
var clineMessageSchema = z7.object({
  ts: z7.number(),
  type: z7.union([z7.literal("ask"), z7.literal("say")]),
  ask: clineAskSchema.optional(),
  say: clineSaySchema.optional(),
  text: z7.string().optional(),
  images: z7.array(z7.string()).optional(),
  partial: z7.boolean().optional(),
  reasoning: z7.string().optional(),
  conversationHistoryIndex: z7.number().optional(),
  checkpoint: z7.record(z7.string(), z7.unknown()).optional(),
  progressStatus: toolProgressStatusSchema.optional(),
  contextCondense: contextCondenseSchema.optional()
});
var tokenUsageSchema = z7.object({
  totalTokensIn: z7.number(),
  totalTokensOut: z7.number(),
  totalCacheWrites: z7.number().optional(),
  totalCacheReads: z7.number().optional(),
  totalCost: z7.number(),
  contextTokens: z7.number()
});

// src/telemetry.ts
var telemetrySettings = ["unset", "enabled", "disabled"];
var telemetrySettingsSchema = z8.enum(telemetrySettings);
var TelemetryEventName = /* @__PURE__ */ ((TelemetryEventName2) => {
  TelemetryEventName2["TASK_CREATED"] = "Task Created";
  TelemetryEventName2["TASK_RESTARTED"] = "Task Reopened";
  TelemetryEventName2["TASK_COMPLETED"] = "Task Completed";
  TelemetryEventName2["TASK_MESSAGE"] = "Task Message";
  TelemetryEventName2["TASK_CONVERSATION_MESSAGE"] = "Conversation Message";
  TelemetryEventName2["LLM_COMPLETION"] = "LLM Completion";
  TelemetryEventName2["MODE_SWITCH"] = "Mode Switched";
  TelemetryEventName2["TOOL_USED"] = "Tool Used";
  TelemetryEventName2["CHECKPOINT_CREATED"] = "Checkpoint Created";
  TelemetryEventName2["CHECKPOINT_RESTORED"] = "Checkpoint Restored";
  TelemetryEventName2["CHECKPOINT_DIFFED"] = "Checkpoint Diffed";
  TelemetryEventName2["CONTEXT_CONDENSED"] = "Context Condensed";
  TelemetryEventName2["SLIDING_WINDOW_TRUNCATION"] = "Sliding Window Truncation";
  TelemetryEventName2["CODE_ACTION_USED"] = "Code Action Used";
  TelemetryEventName2["PROMPT_ENHANCED"] = "Prompt Enhanced";
  TelemetryEventName2["TITLE_BUTTON_CLICKED"] = "Title Button Clicked";
  TelemetryEventName2["AUTHENTICATION_INITIATED"] = "Authentication Initiated";
  TelemetryEventName2["SCHEMA_VALIDATION_ERROR"] = "Schema Validation Error";
  TelemetryEventName2["DIFF_APPLICATION_ERROR"] = "Diff Application Error";
  TelemetryEventName2["SHELL_INTEGRATION_ERROR"] = "Shell Integration Error";
  TelemetryEventName2["CONSECUTIVE_MISTAKE_ERROR"] = "Consecutive Mistake Error";
  return TelemetryEventName2;
})(TelemetryEventName || {});
var appPropertiesSchema = z8.object({
  appName: z8.string(),
  appVersion: z8.string(),
  vscodeVersion: z8.string(),
  platform: z8.string(),
  editorName: z8.string(),
  language: z8.string(),
  mode: z8.string()
});
var taskPropertiesSchema = z8.object({
  taskId: z8.string().optional(),
  apiProvider: z8.enum(providerNames).optional(),
  modelId: z8.string().optional(),
  diffStrategy: z8.string().optional(),
  isSubtask: z8.boolean().optional()
});
var telemetryPropertiesSchema = z8.object({
  ...appPropertiesSchema.shape,
  ...taskPropertiesSchema.shape
});
var rooCodeTelemetryEventSchema = z8.discriminatedUnion("type", [
  z8.object({
    type: z8.enum([
      "Task Created" /* TASK_CREATED */,
      "Task Reopened" /* TASK_RESTARTED */,
      "Task Completed" /* TASK_COMPLETED */,
      "Conversation Message" /* TASK_CONVERSATION_MESSAGE */,
      "Mode Switched" /* MODE_SWITCH */,
      "Tool Used" /* TOOL_USED */,
      "Checkpoint Created" /* CHECKPOINT_CREATED */,
      "Checkpoint Restored" /* CHECKPOINT_RESTORED */,
      "Checkpoint Diffed" /* CHECKPOINT_DIFFED */,
      "Code Action Used" /* CODE_ACTION_USED */,
      "Prompt Enhanced" /* PROMPT_ENHANCED */,
      "Title Button Clicked" /* TITLE_BUTTON_CLICKED */,
      "Authentication Initiated" /* AUTHENTICATION_INITIATED */,
      "Schema Validation Error" /* SCHEMA_VALIDATION_ERROR */,
      "Diff Application Error" /* DIFF_APPLICATION_ERROR */,
      "Shell Integration Error" /* SHELL_INTEGRATION_ERROR */,
      "Consecutive Mistake Error" /* CONSECUTIVE_MISTAKE_ERROR */,
      "Context Condensed" /* CONTEXT_CONDENSED */,
      "Sliding Window Truncation" /* SLIDING_WINDOW_TRUNCATION */
    ]),
    properties: telemetryPropertiesSchema
  }),
  z8.object({
    type: z8.literal("Task Message" /* TASK_MESSAGE */),
    properties: z8.object({
      ...telemetryPropertiesSchema.shape,
      taskId: z8.string(),
      message: clineMessageSchema
    })
  }),
  z8.object({
    type: z8.literal("LLM Completion" /* LLM_COMPLETION */),
    properties: z8.object({
      ...telemetryPropertiesSchema.shape,
      inputTokens: z8.number(),
      outputTokens: z8.number(),
      cacheReadTokens: z8.number().optional(),
      cacheWriteTokens: z8.number().optional(),
      cost: z8.number().optional()
    })
  })
]);

// src/mode.ts
import { z as z10 } from "zod";

// src/tool.ts
import { z as z9 } from "zod";
var toolGroups = ["read", "edit", "browser", "command", "mcp", "modes"];
var toolGroupsSchema = z9.enum(toolGroups);
var toolNames = [
  "execute_command",
  "read_file",
  "write_to_file",
  "apply_diff",
  "insert_content",
  "search_and_replace",
  "search_files",
  "list_files",
  "list_code_definition_names",
  "browser_action",
  "use_mcp_tool",
  "access_mcp_resource",
  "ask_followup_question",
  "attempt_completion",
  "switch_mode",
  "new_task",
  "fetch_instructions",
  "codebase_search"
];
var toolNamesSchema = z9.enum(toolNames);
var toolUsageSchema = z9.record(
  toolNamesSchema,
  z9.object({
    attempts: z9.number(),
    failures: z9.number()
  })
);

// src/mode.ts
var groupOptionsSchema = z10.object({
  fileRegex: z10.string().optional().refine(
    (pattern) => {
      if (!pattern) {
        return true;
      }
      try {
        new RegExp(pattern);
        return true;
      } catch {
        return false;
      }
    },
    { message: "Invalid regular expression pattern" }
  ),
  description: z10.string().optional()
});
var groupEntrySchema = z10.union([toolGroupsSchema, z10.tuple([toolGroupsSchema, groupOptionsSchema])]);
var groupEntryArraySchema = z10.array(groupEntrySchema).refine(
  (groups) => {
    const seen = /* @__PURE__ */ new Set();
    return groups.every((group) => {
      const groupName = Array.isArray(group) ? group[0] : group;
      if (seen.has(groupName)) {
        return false;
      }
      seen.add(groupName);
      return true;
    });
  },
  { message: "Duplicate groups are not allowed" }
);
var modeConfigSchema = z10.object({
  slug: z10.string().regex(/^[a-zA-Z0-9-]+$/, "Slug must contain only letters numbers and dashes"),
  name: z10.string().min(1, "Name is required"),
  roleDefinition: z10.string().min(1, "Role definition is required"),
  whenToUse: z10.string().optional(),
  customInstructions: z10.string().optional(),
  groups: groupEntryArraySchema,
  source: z10.enum(["global", "project"]).optional()
});
var customModesSettingsSchema = z10.object({
  customModes: z10.array(modeConfigSchema).refine(
    (modes) => {
      const slugs = /* @__PURE__ */ new Set();
      return modes.every((mode) => {
        if (slugs.has(mode.slug)) {
          return false;
        }
        slugs.add(mode.slug);
        return true;
      });
    },
    {
      message: "Duplicate mode slugs are not allowed"
    }
  )
});
var promptComponentSchema = z10.object({
  roleDefinition: z10.string().optional(),
  whenToUse: z10.string().optional(),
  customInstructions: z10.string().optional()
});
var customModePromptsSchema = z10.record(z10.string(), promptComponentSchema.optional());
var customSupportPromptsSchema = z10.record(z10.string(), z10.string().optional());

// src/vscode.ts
import { z as z11 } from "zod";
var codeActionIds = ["explainCode", "fixCode", "improveCode", "addToContext", "newTask"];
var terminalActionIds = ["terminalAddToContext", "terminalFixCommand", "terminalExplainCommand"];
var commandIds = [
  "activationCompleted",
  "plusButtonClicked",
  "promptsButtonClicked",
  "mcpButtonClicked",
  "historyButtonClicked",
  "popoutButtonClicked",
  "accountButtonClicked",
  "usageButtonClicked",
  "settingsButtonClicked",
  "openInNewTab",
  "showHumanRelayDialog",
  "registerHumanRelayCallback",
  "unregisterHumanRelayCallback",
  "handleHumanRelayResponse",
  "newTask",
  "setCustomStoragePath",
  "clearModeCache",
  "focusInput",
  "acceptInput"
];
var languages = [
  "ca",
  "de",
  "en",
  "es",
  "fr",
  "hi",
  "it",
  "ja",
  "ko",
  "nl",
  "pl",
  "pt-BR",
  "ru",
  "tr",
  "vi",
  "zh-CN",
  "zh-TW"
];
var languagesSchema = z11.enum(languages);
var isLanguage = (value) => languages.includes(value);

// src/global-settings.ts
var globalSettingsSchema = z12.object({
  currentApiConfigName: z12.string().optional(),
  listApiConfigMeta: z12.array(providerSettingsEntrySchema).optional(),
  pinnedApiConfigs: z12.record(z12.string(), z12.boolean()).optional(),
  lastShownAnnouncementId: z12.string().optional(),
  customInstructions: z12.string().optional(),
  taskHistory: z12.array(historyItemSchema).optional(),
  condensingApiConfigId: z12.string().optional(),
  customCondensingPrompt: z12.string().optional(),
  autoApprovalEnabled: z12.boolean().optional(),
  alwaysAllowReadOnly: z12.boolean().optional(),
  alwaysAllowReadOnlyOutsideWorkspace: z12.boolean().optional(),
  alwaysAllowWrite: z12.boolean().optional(),
  alwaysAllowWriteOutsideWorkspace: z12.boolean().optional(),
  writeDelayMs: z12.number().optional(),
  alwaysAllowBrowser: z12.boolean().optional(),
  alwaysApproveResubmit: z12.boolean().optional(),
  requestDelaySeconds: z12.number().optional(),
  alwaysAllowMcp: z12.boolean().optional(),
  alwaysAllowModeSwitch: z12.boolean().optional(),
  alwaysAllowSubtasks: z12.boolean().optional(),
  alwaysAllowExecute: z12.boolean().optional(),
  allowedCommands: z12.array(z12.string()).optional(),
  allowedMaxRequests: z12.number().nullish(),
  autoCondenseContext: z12.boolean().optional(),
  autoCondenseContextPercent: z12.number().optional(),
  maxConcurrentFileReads: z12.number().optional(),
  browserToolEnabled: z12.boolean().optional(),
  browserViewportSize: z12.string().optional(),
  screenshotQuality: z12.number().optional(),
  remoteBrowserEnabled: z12.boolean().optional(),
  remoteBrowserHost: z12.string().optional(),
  cachedChromeHostUrl: z12.string().optional(),
  enableCheckpoints: z12.boolean().optional(),
  ttsEnabled: z12.boolean().optional(),
  ttsSpeed: z12.number().optional(),
  soundEnabled: z12.boolean().optional(),
  soundVolume: z12.number().optional(),
  maxOpenTabsContext: z12.number().optional(),
  maxWorkspaceFiles: z12.number().optional(),
  showRooIgnoredFiles: z12.boolean().optional(),
  maxReadFileLine: z12.number().optional(),
  terminalOutputLineLimit: z12.number().optional(),
  terminalShellIntegrationTimeout: z12.number().optional(),
  terminalShellIntegrationDisabled: z12.boolean().optional(),
  terminalCommandDelay: z12.number().optional(),
  terminalPowershellCounter: z12.boolean().optional(),
  terminalZshClearEolMark: z12.boolean().optional(),
  terminalZshOhMy: z12.boolean().optional(),
  terminalZshP10k: z12.boolean().optional(),
  terminalZdotdir: z12.boolean().optional(),
  terminalCompressProgressBar: z12.boolean().optional(),
  rateLimitSeconds: z12.number().optional(),
  diffEnabled: z12.boolean().optional(),
  fuzzyMatchThreshold: z12.number().optional(),
  experiments: experimentsSchema.optional(),
  codebaseIndexModels: codebaseIndexModelsSchema.optional(),
  codebaseIndexConfig: codebaseIndexConfigSchema.optional(),
  language: languagesSchema.optional(),
  telemetrySetting: telemetrySettingsSchema.optional(),
  mcpEnabled: z12.boolean().optional(),
  enableMcpServerCreation: z12.boolean().optional(),
  mode: z12.string().optional(),
  modeApiConfigs: z12.record(z12.string(), z12.string()).optional(),
  customModes: z12.array(modeConfigSchema).optional(),
  customModePrompts: customModePromptsSchema.optional(),
  customSupportPrompts: customSupportPromptsSchema.optional(),
  enhancementApiConfigId: z12.string().optional(),
  historyPreviewCollapsed: z12.boolean().optional()
});
var GLOBAL_SETTINGS_KEYS = keysOf()([
  "currentApiConfigName",
  "listApiConfigMeta",
  "pinnedApiConfigs",
  "lastShownAnnouncementId",
  "customInstructions",
  "taskHistory",
  "condensingApiConfigId",
  "customCondensingPrompt",
  "autoApprovalEnabled",
  "alwaysAllowReadOnly",
  "alwaysAllowReadOnlyOutsideWorkspace",
  "alwaysAllowWrite",
  "alwaysAllowWriteOutsideWorkspace",
  "writeDelayMs",
  "alwaysAllowBrowser",
  "alwaysApproveResubmit",
  "requestDelaySeconds",
  "alwaysAllowMcp",
  "alwaysAllowModeSwitch",
  "alwaysAllowSubtasks",
  "alwaysAllowExecute",
  "allowedCommands",
  "allowedMaxRequests",
  "autoCondenseContext",
  "autoCondenseContextPercent",
  "maxConcurrentFileReads",
  "browserToolEnabled",
  "browserViewportSize",
  "screenshotQuality",
  "remoteBrowserEnabled",
  "remoteBrowserHost",
  "enableCheckpoints",
  "ttsEnabled",
  "ttsSpeed",
  "soundEnabled",
  "soundVolume",
  "maxOpenTabsContext",
  "maxWorkspaceFiles",
  "showRooIgnoredFiles",
  "maxReadFileLine",
  "terminalOutputLineLimit",
  "terminalShellIntegrationTimeout",
  "terminalShellIntegrationDisabled",
  "terminalCommandDelay",
  "terminalPowershellCounter",
  "terminalZshClearEolMark",
  "terminalZshOhMy",
  "terminalZshP10k",
  "terminalZdotdir",
  "terminalCompressProgressBar",
  "rateLimitSeconds",
  "diffEnabled",
  "fuzzyMatchThreshold",
  "experiments",
  "codebaseIndexModels",
  "codebaseIndexConfig",
  "language",
  "telemetrySetting",
  "mcpEnabled",
  "enableMcpServerCreation",
  "mode",
  "modeApiConfigs",
  "customModes",
  "customModePrompts",
  "customSupportPrompts",
  "enhancementApiConfigId",
  "cachedChromeHostUrl",
  "historyPreviewCollapsed"
]);
var rooCodeSettingsSchema = providerSettingsSchema.merge(globalSettingsSchema);
var SECRET_STATE_KEYS = keysOf()([
  "apiKey",
  "glamaApiKey",
  "openRouterApiKey",
  "awsAccessKey",
  "awsSecretKey",
  "awsSessionToken",
  "openAiApiKey",
  "geminiApiKey",
  "openAiNativeApiKey",
  "deepSeekApiKey",
  "mistralApiKey",
  "unboundApiKey",
  "requestyApiKey",
  "xaiApiKey",
  "groqApiKey",
  "chutesApiKey",
  "litellmApiKey",
  "codeIndexOpenAiKey",
  "codeIndexQdrantApiKey"
]);
var isSecretStateKey = (key) => SECRET_STATE_KEYS.includes(key);
var GLOBAL_STATE_KEYS = [...GLOBAL_SETTINGS_KEYS, ...PROVIDER_SETTINGS_KEYS].filter(
  (key) => !SECRET_STATE_KEYS.includes(key)
);
var isGlobalStateKey = (key) => GLOBAL_STATE_KEYS.includes(key);

// src/ipc.ts
import { z as z13 } from "zod";
var RooCodeEventName = /* @__PURE__ */ ((RooCodeEventName2) => {
  RooCodeEventName2["Message"] = "message";
  RooCodeEventName2["TaskCreated"] = "taskCreated";
  RooCodeEventName2["TaskStarted"] = "taskStarted";
  RooCodeEventName2["TaskModeSwitched"] = "taskModeSwitched";
  RooCodeEventName2["TaskPaused"] = "taskPaused";
  RooCodeEventName2["TaskUnpaused"] = "taskUnpaused";
  RooCodeEventName2["TaskAskResponded"] = "taskAskResponded";
  RooCodeEventName2["TaskAborted"] = "taskAborted";
  RooCodeEventName2["TaskSpawned"] = "taskSpawned";
  RooCodeEventName2["TaskCompleted"] = "taskCompleted";
  RooCodeEventName2["TaskTokenUsageUpdated"] = "taskTokenUsageUpdated";
  RooCodeEventName2["TaskToolFailed"] = "taskToolFailed";
  return RooCodeEventName2;
})(RooCodeEventName || {});
var rooCodeEventsSchema = z13.object({
  ["message" /* Message */]: z13.tuple([
    z13.object({
      taskId: z13.string(),
      action: z13.union([z13.literal("created"), z13.literal("updated")]),
      message: clineMessageSchema
    })
  ]),
  ["taskCreated" /* TaskCreated */]: z13.tuple([z13.string()]),
  ["taskStarted" /* TaskStarted */]: z13.tuple([z13.string()]),
  ["taskModeSwitched" /* TaskModeSwitched */]: z13.tuple([z13.string(), z13.string()]),
  ["taskPaused" /* TaskPaused */]: z13.tuple([z13.string()]),
  ["taskUnpaused" /* TaskUnpaused */]: z13.tuple([z13.string()]),
  ["taskAskResponded" /* TaskAskResponded */]: z13.tuple([z13.string()]),
  ["taskAborted" /* TaskAborted */]: z13.tuple([z13.string()]),
  ["taskSpawned" /* TaskSpawned */]: z13.tuple([z13.string(), z13.string()]),
  ["taskCompleted" /* TaskCompleted */]: z13.tuple([z13.string(), tokenUsageSchema, toolUsageSchema]),
  ["taskTokenUsageUpdated" /* TaskTokenUsageUpdated */]: z13.tuple([z13.string(), tokenUsageSchema]),
  ["taskToolFailed" /* TaskToolFailed */]: z13.tuple([z13.string(), toolNamesSchema, z13.string()])
});
var ackSchema = z13.object({
  clientId: z13.string(),
  pid: z13.number(),
  ppid: z13.number()
});
var TaskCommandName = /* @__PURE__ */ ((TaskCommandName2) => {
  TaskCommandName2["StartNewTask"] = "StartNewTask";
  TaskCommandName2["CancelTask"] = "CancelTask";
  TaskCommandName2["CloseTask"] = "CloseTask";
  return TaskCommandName2;
})(TaskCommandName || {});
var taskCommandSchema = z13.discriminatedUnion("commandName", [
  z13.object({
    commandName: z13.literal("StartNewTask" /* StartNewTask */),
    data: z13.object({
      configuration: rooCodeSettingsSchema,
      text: z13.string(),
      images: z13.array(z13.string()).optional(),
      newTab: z13.boolean().optional()
    })
  }),
  z13.object({
    commandName: z13.literal("CancelTask" /* CancelTask */),
    data: z13.string()
  }),
  z13.object({
    commandName: z13.literal("CloseTask" /* CloseTask */),
    data: z13.string()
  })
]);
var taskEventSchema = z13.discriminatedUnion("eventName", [
  z13.object({
    eventName: z13.literal("message" /* Message */),
    payload: rooCodeEventsSchema.shape["message" /* Message */]
  }),
  z13.object({
    eventName: z13.literal("taskCreated" /* TaskCreated */),
    payload: rooCodeEventsSchema.shape["taskCreated" /* TaskCreated */]
  }),
  z13.object({
    eventName: z13.literal("taskStarted" /* TaskStarted */),
    payload: rooCodeEventsSchema.shape["taskStarted" /* TaskStarted */]
  }),
  z13.object({
    eventName: z13.literal("taskModeSwitched" /* TaskModeSwitched */),
    payload: rooCodeEventsSchema.shape["taskModeSwitched" /* TaskModeSwitched */]
  }),
  z13.object({
    eventName: z13.literal("taskPaused" /* TaskPaused */),
    payload: rooCodeEventsSchema.shape["taskPaused" /* TaskPaused */]
  }),
  z13.object({
    eventName: z13.literal("taskUnpaused" /* TaskUnpaused */),
    payload: rooCodeEventsSchema.shape["taskUnpaused" /* TaskUnpaused */]
  }),
  z13.object({
    eventName: z13.literal("taskAskResponded" /* TaskAskResponded */),
    payload: rooCodeEventsSchema.shape["taskAskResponded" /* TaskAskResponded */]
  }),
  z13.object({
    eventName: z13.literal("taskAborted" /* TaskAborted */),
    payload: rooCodeEventsSchema.shape["taskAborted" /* TaskAborted */]
  }),
  z13.object({
    eventName: z13.literal("taskSpawned" /* TaskSpawned */),
    payload: rooCodeEventsSchema.shape["taskSpawned" /* TaskSpawned */]
  }),
  z13.object({
    eventName: z13.literal("taskCompleted" /* TaskCompleted */),
    payload: rooCodeEventsSchema.shape["taskCompleted" /* TaskCompleted */]
  }),
  z13.object({
    eventName: z13.literal("taskTokenUsageUpdated" /* TaskTokenUsageUpdated */),
    payload: rooCodeEventsSchema.shape["taskTokenUsageUpdated" /* TaskTokenUsageUpdated */]
  })
]);
var IpcMessageType = /* @__PURE__ */ ((IpcMessageType2) => {
  IpcMessageType2["Connect"] = "Connect";
  IpcMessageType2["Disconnect"] = "Disconnect";
  IpcMessageType2["Ack"] = "Ack";
  IpcMessageType2["TaskCommand"] = "TaskCommand";
  IpcMessageType2["TaskEvent"] = "TaskEvent";
  return IpcMessageType2;
})(IpcMessageType || {});
var IpcOrigin = /* @__PURE__ */ ((IpcOrigin2) => {
  IpcOrigin2["Client"] = "client";
  IpcOrigin2["Server"] = "server";
  return IpcOrigin2;
})(IpcOrigin || {});
var ipcMessageSchema = z13.discriminatedUnion("type", [
  z13.object({
    type: z13.literal("Ack" /* Ack */),
    origin: z13.literal("server" /* Server */),
    data: ackSchema
  }),
  z13.object({
    type: z13.literal("TaskCommand" /* TaskCommand */),
    origin: z13.literal("client" /* Client */),
    clientId: z13.string(),
    data: taskCommandSchema
  }),
  z13.object({
    type: z13.literal("TaskEvent" /* TaskEvent */),
    origin: z13.literal("server" /* Server */),
    relayClientId: z13.string().optional(),
    data: taskEventSchema
  })
]);

// src/terminal.ts
import { z as z14 } from "zod";
var commandExecutionStatusSchema = z14.discriminatedUnion("status", [
  z14.object({
    executionId: z14.string(),
    status: z14.literal("started"),
    pid: z14.number().optional(),
    command: z14.string()
  }),
  z14.object({
    executionId: z14.string(),
    status: z14.literal("output"),
    output: z14.string()
  }),
  z14.object({
    executionId: z14.string(),
    status: z14.literal("exited"),
    exitCode: z14.number().optional()
  }),
  z14.object({
    executionId: z14.string(),
    status: z14.literal("fallback")
  })
]);

// src/user-management.ts
import { z as z15 } from "zod";
var SubscriptionTier = /* @__PURE__ */ ((SubscriptionTier2) => {
  SubscriptionTier2["FREE_TRIAL"] = "free_trial";
  SubscriptionTier2["BASIC"] = "basic";
  SubscriptionTier2["PRO"] = "pro";
  SubscriptionTier2["ENTERPRISE"] = "enterprise";
  return SubscriptionTier2;
})(SubscriptionTier || {});
var SubscriptionStatus = /* @__PURE__ */ ((SubscriptionStatus2) => {
  SubscriptionStatus2["ACTIVE"] = "active";
  SubscriptionStatus2["TRIAL"] = "trial";
  SubscriptionStatus2["EXPIRED"] = "expired";
  SubscriptionStatus2["CANCELLED"] = "cancelled";
  SubscriptionStatus2["SUSPENDED"] = "suspended";
  return SubscriptionStatus2;
})(SubscriptionStatus || {});
var SUBSCRIPTION_PLANS = {
  ["free_trial" /* FREE_TRIAL */]: {
    monthlyTokenLimit: 1e5,
    // 100K tokens
    monthlyCostLimit: 10,
    // $10
    hourlyRequestLimit: 50,
    dailyRequestLimit: 500,
    maxContextWindow: 32e3,
    allowedModels: [
      "Claude 3.5 Sonnet",
      "GPT-4o Mini",
      "Gemini 1.5 Flash"
    ],
    canUseReasoningModels: false,
    canUseCodebaseIndex: false,
    canUseCustomModes: false,
    canExportHistory: false
  },
  ["basic" /* BASIC */]: {
    monthlyTokenLimit: 1e6,
    // 1M tokens
    monthlyCostLimit: 50,
    // $50
    hourlyRequestLimit: 200,
    dailyRequestLimit: 2e3,
    maxContextWindow: 128e3,
    allowedModels: [
      "Claude 3.5 Sonnet",
      "Claude Sonnet 4",
      "GPT-4o",
      "GPT-4o Mini",
      "Gemini 1.5 Pro",
      "Gemini 1.5 Flash"
    ],
    canUseReasoningModels: false,
    canUseCodebaseIndex: true,
    canUseCustomModes: true,
    canExportHistory: true
  },
  ["pro" /* PRO */]: {
    monthlyTokenLimit: 5e6,
    // 5M tokens
    monthlyCostLimit: 200,
    // $200
    hourlyRequestLimit: 500,
    dailyRequestLimit: 5e3,
    maxContextWindow: 2e5,
    allowedModels: [
      "Claude 3.5 Sonnet",
      "Claude Sonnet 4",
      "Claude 3.7 Sonnet (Thinking)",
      "GPT-4o",
      "GPT-4o Mini",
      "o1-preview",
      "o1-mini",
      "Gemini 1.5 Pro",
      "Gemini 2.0 Pro",
      "DeepSeek V3"
    ],
    canUseReasoningModels: true,
    canUseCodebaseIndex: true,
    canUseCustomModes: true,
    canExportHistory: true
  },
  ["enterprise" /* ENTERPRISE */]: {
    monthlyTokenLimit: 2e7,
    // 20M tokens
    monthlyCostLimit: 1e3,
    // $1000
    hourlyRequestLimit: 2e3,
    dailyRequestLimit: 2e4,
    maxContextWindow: 1e6,
    allowedModels: [],
    // All models allowed
    canUseReasoningModels: true,
    canUseCodebaseIndex: true,
    canUseCustomModes: true,
    canExportHistory: true
  }
};
var usageQuotasSchema = z15.object({
  monthlyTokenLimit: z15.number().min(0),
  monthlyCostLimit: z15.number().min(0),
  hourlyRequestLimit: z15.number().min(0),
  dailyRequestLimit: z15.number().min(0),
  maxContextWindow: z15.number().min(0),
  allowedModels: z15.array(z15.string()),
  canUseReasoningModels: z15.boolean(),
  canUseCodebaseIndex: z15.boolean(),
  canUseCustomModes: z15.boolean(),
  canExportHistory: z15.boolean()
});
var usageMetricsSchema = z15.object({
  currentMonthTokens: z15.number().min(0),
  currentMonthCost: z15.number().min(0),
  currentHourRequests: z15.number().min(0),
  currentDayRequests: z15.number().min(0),
  totalTokensUsed: z15.number().min(0),
  totalCostAccrued: z15.number().min(0),
  totalRequestsMade: z15.number().min(0),
  lastMonthlyReset: z15.date(),
  lastHourlyReset: z15.date(),
  lastDailyReset: z15.date(),
  modelUsage: z15.record(z15.object({
    tokens: z15.number().min(0),
    cost: z15.number().min(0),
    requests: z15.number().min(0)
  }))
});
var userPreferencesSchema = z15.object({
  usageWarningsEnabled: z15.boolean(),
  trialExpiryNotifications: z15.boolean(),
  detailedUsageTracking: z15.boolean(),
  costAlertsEnabled: z15.boolean(),
  costAlertThreshold: z15.number().min(0).max(100),
  autoUpgradeEnabled: z15.boolean(),
  preferredUpgradeTier: z15.nativeEnum(SubscriptionTier)
});
var userProfileSchema = z15.object({
  id: z15.string(),
  email: z15.string().email(),
  name: z15.string().optional(),
  picture: z15.string().optional(),
  subscriptionTier: z15.nativeEnum(SubscriptionTier),
  subscriptionStatus: z15.nativeEnum(SubscriptionStatus),
  subscriptionStartDate: z15.date(),
  subscriptionEndDate: z15.date().optional(),
  trialStartDate: z15.date().optional(),
  trialEndDate: z15.date().optional(),
  trialExtensions: z15.number().min(0),
  quotas: usageQuotasSchema,
  usage: usageMetricsSchema,
  preferences: userPreferencesSchema,
  createdAt: z15.date(),
  updatedAt: z15.date(),
  lastActiveAt: z15.date()
});
export {
  ANTHROPIC_DEFAULT_MAX_TOKENS,
  BEDROCK_DEFAULT_TEMPERATURE,
  BEDROCK_MAX_TOKENS,
  BEDROCK_REGIONS,
  BEDROCK_REGION_INFO,
  DEEP_SEEK_DEFAULT_TEMPERATURE,
  GLAMA_DEFAULT_TEMPERATURE,
  GLOBAL_SETTINGS_KEYS,
  GLOBAL_STATE_KEYS,
  IpcMessageType,
  IpcOrigin,
  LITELLM_COMPUTER_USE_MODELS,
  LMSTUDIO_DEFAULT_TEMPERATURE,
  MISTRAL_DEFAULT_TEMPERATURE,
  OPENAI_AZURE_AI_INFERENCE_PATH,
  OPENAI_NATIVE_DEFAULT_TEMPERATURE,
  OPENROUTER_DEFAULT_PROVIDER_NAME,
  OPEN_ROUTER_COMPUTER_USE_MODELS,
  OPEN_ROUTER_PROMPT_CACHING_MODELS,
  OPEN_ROUTER_REASONING_BUDGET_MODELS,
  OPEN_ROUTER_REQUIRED_REASONING_BUDGET_MODELS,
  ORGANIZATION_ALLOW_ALL,
  PROVIDER_SETTINGS_KEYS,
  RooCodeEventName,
  SECRET_STATE_KEYS,
  SUBSCRIPTION_PLANS,
  SubscriptionStatus,
  SubscriptionTier,
  TaskCommandName,
  TelemetryEventName,
  VERTEX_REGIONS,
  ackSchema,
  anthropicDefaultModelId,
  anthropicModels,
  appPropertiesSchema,
  azureOpenAiDefaultApiVersion,
  bedrockDefaultModelId,
  bedrockDefaultPromptRouterModelId,
  bedrockModels,
  chutesDefaultModelId,
  chutesModels,
  clineAskSchema,
  clineAsks,
  clineMessageSchema,
  clineSaySchema,
  clineSays,
  codeActionIds,
  codebaseIndexConfigSchema,
  codebaseIndexModelsSchema,
  codebaseIndexProviderSchema,
  commandExecutionStatusSchema,
  commandIds,
  contextCondenseSchema,
  customModePromptsSchema,
  customModesSettingsSchema,
  customSupportPromptsSchema,
  deepSeekDefaultModelId,
  deepSeekModels,
  experimentIds,
  experimentIdsSchema,
  experimentsSchema,
  geminiDefaultModelId,
  geminiModels,
  glamaDefaultModelId,
  glamaDefaultModelInfo,
  globalSettingsSchema,
  groqDefaultModelId,
  groqModels,
  groupEntrySchema,
  groupOptionsSchema,
  historyItemSchema,
  ipcMessageSchema,
  isGlobalStateKey,
  isLanguage,
  isModelParameter,
  isSecretStateKey,
  keysOf,
  languages,
  languagesSchema,
  litellmDefaultModelId,
  litellmDefaultModelInfo,
  mistralDefaultModelId,
  mistralModels,
  modeConfigSchema,
  modelInfoSchema,
  modelParameters,
  modelParametersSchema,
  openAiModelInfoSaneDefaults,
  openAiNativeDefaultModelId,
  openAiNativeModels,
  openRouterDefaultModelId,
  openRouterDefaultModelInfo,
  organizationAllowListSchema,
  organizationSettingsSchema,
  promptComponentSchema,
  providerNames,
  providerNamesSchema,
  providerSettingsEntrySchema,
  providerSettingsSchema,
  providerSettingsSchemaDiscriminated,
  reasoningEfforts,
  reasoningEffortsSchema,
  requestyDefaultModelId,
  requestyDefaultModelInfo,
  rooCodeEventsSchema,
  rooCodeSettingsSchema,
  rooCodeTelemetryEventSchema,
  taskCommandSchema,
  taskEventSchema,
  taskPropertiesSchema,
  telemetryPropertiesSchema,
  telemetrySettings,
  telemetrySettingsSchema,
  terminalActionIds,
  tokenUsageSchema,
  toolGroups,
  toolGroupsSchema,
  toolNames,
  toolNamesSchema,
  toolProgressStatusSchema,
  toolUsageSchema,
  unboundDefaultModelId,
  unboundDefaultModelInfo,
  usageMetricsSchema,
  usageQuotasSchema,
  userPreferencesSchema,
  userProfileSchema,
  vertexDefaultModelId,
  vertexModels,
  vscodeLlmDefaultModelId,
  vscodeLlmModels,
  xaiDefaultModelId,
  xaiModels
};
//# sourceMappingURL=index.js.map