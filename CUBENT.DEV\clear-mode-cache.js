// Clear the customModePrompts cache that's overriding built-in mode configurations
const vscode = require('vscode');

async function clearCustomModePromptsCache() {
    try {
        // Get the extension context
        const extension = vscode.extensions.getExtension('cubent.cubent');
        if (!extension) {
            console.error('Cubent extension not found');
            return;
        }

        // Access the global state through the extension's context
        const context = extension.exports?.context;
        if (!context) {
            console.error('Extension context not available');
            return;
        }

        // Clear the customModePrompts cache
        await context.globalState.update('customModePrompts', undefined);
        console.log('Successfully cleared customModePrompts cache');
        
        // Also clear any other mode-related caches
        await context.globalState.update('customModes', undefined);
        console.log('Successfully cleared customModes cache');
        
        vscode.window.showInformationMessage('Mode caches cleared successfully');
    } catch (error) {
        console.error('Error clearing mode caches:', error);
        vscode.window.showErrorMessage('Failed to clear mode caches: ' + error.message);
    }
}

// Export for use in extension
module.exports = { clearCustomModePromptsCache };
