// <PERSON>ript to clear agent-auto mode cache by directly modifying extension global state
// This will be executed as a temporary command to clear the cached customModePrompts

const fs = require('fs');
const path = require('path');
const os = require('os');

// VS Code stores extension global state in a JSON file
// The path varies by OS and VS Code version
function getVSCodeStoragePath() {
    const platform = os.platform();
    const homeDir = os.homedir();

    if (platform === 'win32') {
        return path.join(homeDir, 'AppData', 'Roaming', 'Code', 'User', 'globalStorage', 'cubent.cubent');
    } else if (platform === 'darwin') {
        return path.join(homeDir, 'Library', 'Application Support', 'Code', 'User', 'globalStorage', 'cubent.cubent');
    } else {
        return path.join(homeDir, '.config', 'Code', 'User', 'globalStorage', 'cubent.cubent');
    }
}

async function clearAgentAutoCache() {
    try {
        const storagePath = getVSCodeStoragePath();
        const stateFile = path.join(storagePath, 'state.vscdb');

        console.log('Looking for VS Code storage at:', storagePath);
        console.log('State file:', stateFile);

        if (fs.existsSync(stateFile)) {
            console.log('Found VS Code state file');
            // VS Code uses a binary format, so we can't easily modify it
            console.log('Cannot directly modify binary state file');
        } else {
            console.log('VS Code state file not found');
        }

        // Alternative: Look for JSON files
        if (fs.existsSync(storagePath)) {
            const files = fs.readdirSync(storagePath);
            console.log('Files in storage directory:', files);
        }

    } catch (error) {
        console.error('Error:', error.message);
    }
}

clearAgentAutoCache();
