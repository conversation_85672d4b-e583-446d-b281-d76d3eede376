// Instructions to clear agent-auto mode cache manually:
//
// 1. Open VS Code Command Palette (Ctrl+Shift+P)
// 2. Type "Developer: Reload Window" and execute it
// 3. If that doesn't work, try these steps:
//    a. Open Cubent sidebar
//    b. Click the "Modes" button (organization icon)
//    c. Select "agent-auto" mode
//    d. Click the reset button (discard icon) next to each section:
//       - Role Definition
//       - When to Use
//       - Custom Instructions
//    e. Click "Done"
//
// This will clear the cached customModePrompts overrides and show the updated built-in mode configuration.

console.log('Use the manual steps above to clear the agent-auto mode cache');
